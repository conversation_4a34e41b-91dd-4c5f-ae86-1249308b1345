/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.10.7 (2022-12-06)
 */
!(() => {
  function i(e) {
    var t = e;
    return {
      get() {
        return t;
      },
      set(e) {
        t = e;
      },
    };
  }
  function _(e) {
    return e.getParam('fullpage_hide_in_source_view');
  }
  function b(e) {
    return e.getParam('fullpage_default_encoding');
  }
  function x(e) {
    return e.getParam('fullpage_default_font_family');
  }
  function k(e) {
    return e.getParam('fullpage_default_font_size');
  }
  function C(e, t) {
    return n({ validate: !1, root_name: '#document' }, e.schema).parse(t, {
      format: 'xhtml',
    });
  }
  function c(u, m) {
    u.addCommand('mceFullPageProperties', () => {
      var l, i, o, e, t, n, r, a, c, s;
      function d(e, t) {
        return e.attr(t) || '';
      }
      (t = l = u),
        (n = (i = m).get()),
        (c = C(t, n)),
        ((s = {}).fontface = x(t)),
        (s.fontsize = k(t)),
        (r = c.firstChild).type === 7 &&
          ((s.xml_pi = !0),
          (a = /encoding="([^"]+)"/.exec(r.value)) && (s.docencoding = a[1])),
        (r = c.getAll('#doctype')[0]) &&
          (s.doctype = '<!DOCTYPE' + r.value + '>'),
        (r = c.getAll('title')[0]) &&
          r.firstChild &&
          (s.title = r.firstChild.value),
        w.each(c.getAll('meta'), (e) => {
          var t,
            n = e.attr('name'),
            l = e.attr('http-equiv');
          n
            ? (s[n.toLowerCase()] = e.attr('content'))
            : l === 'Content-Type' &&
              (t = /charset\s*=\s*(.*)\s*/gi.exec(e.attr('content'))) &&
              (s.docencoding = t[1]);
        }),
        (r = c.getAll('html')[0]) &&
          (s.langcode = d(r, 'lang') || d(r, 'xml:lang')),
        (s.stylesheets = []),
        w.each(c.getAll('link'), (e) => {
          e.attr('rel') === 'stylesheet' && s.stylesheets.push(e.attr('href'));
        }),
        (r = c.getAll('body')[0]) &&
          ((s.langdir = d(r, 'dir')),
          (s.style = d(r, 'style')),
          (s.visited_color = d(r, 'vlink')),
          (s.link_color = d(r, 'link')),
          (s.active_color = d(r, 'alink'))),
        (o = s),
        (e = g(
          g(
            {},
            {
              title: '',
              keywords: '',
              description: '',
              robots: '',
              author: '',
              docencoding: '',
            }
          ),
          o
        )),
        l.windowManager.open({
          title: 'Metadata and Document Properties',
          size: 'normal',
          body: {
            type: 'panel',
            items: [
              { name: 'title', type: 'input', label: 'Title' },
              { name: 'keywords', type: 'input', label: 'Keywords' },
              { name: 'description', type: 'input', label: 'Description' },
              { name: 'robots', type: 'input', label: 'Robots' },
              { name: 'author', type: 'input', label: 'Author' },
              { name: 'docencoding', type: 'input', label: 'Encoding' },
            ],
          },
          buttons: [
            { type: 'cancel', name: 'cancel', text: 'Cancel' },
            { type: 'submit', name: 'save', text: 'Save', primary: !0 },
          ],
          initialData: e,
          onSubmit(e) {
            var t = e.getData(),
              n = ((e, o, t) => {
                function n(e, t, n) {
                  e.attr(t, n || void 0);
                }
                function r(e) {
                  s.firstChild ? s.insert(e, s.firstChild) : s.append(e);
                }
                var a,
                  l,
                  i = e.dom,
                  c = C(e, t),
                  s = c.getAll('head')[0];
                s ||
                  ((a = c.getAll('html')[0]),
                  (s = new f('head', 1)),
                  a.firstChild ? a.insert(s, a.firstChild, !0) : a.append(s)),
                  (a = c.firstChild),
                  o.xml_pi
                    ? ((l = 'version="1.0"'),
                      o.docencoding &&
                        (l += ' encoding="' + o.docencoding + '"'),
                      a.type !== 7 &&
                        ((a = new f('xml', 7)), c.insert(a, c.firstChild, !0)),
                      (a.value = l))
                    : a && a.type === 7 && a.remove(),
                  (a = c.getAll('#doctype')[0]),
                  o.doctype
                    ? (a ||
                        ((a = new f('#doctype', 10)),
                        o.xml_pi ? c.insert(a, c.firstChild) : r(a)),
                      (a.value = o.doctype.substring(9, o.doctype.length - 1)))
                    : a && a.remove(),
                  (a = null),
                  w.each(c.getAll('meta'), (e) => {
                    e.attr('http-equiv') === 'Content-Type' && (a = e);
                  }),
                  o.docencoding
                    ? (a ||
                        ((a = new f('meta', 1)).attr(
                          'http-equiv',
                          'Content-Type'
                        ),
                        (a.shortEnded = !0),
                        r(a)),
                      a.attr('content', 'text/html; charset=' + o.docencoding))
                    : a && a.remove(),
                  (a = c.getAll('title')[0]),
                  o.title
                    ? (a ? a.empty() : r((a = new f('title', 1))),
                      (a.append(new f('#text', 3)).value = o.title))
                    : a && a.remove(),
                  w.each(
                    'keywords,description,author,copyright,robots'.split(','),
                    (e) => {
                      for (
                        var t, n = c.getAll('meta'), l = o[e], i = 0;
                        i < n.length;
                        i++
                      )
                        if ((t = n[i]).attr('name') === e)
                          return void (l ? t.attr('content', l) : t.remove());
                      l &&
                        ((a = new f('meta', 1)).attr('name', e),
                        a.attr('content', l),
                        (a.shortEnded = !0),
                        r(a));
                    }
                  );
                var d = {};
                w.each(c.getAll('link'), (e) => {
                  e.attr('rel') === 'stylesheet' && (d[e.attr('href')] = e);
                }),
                  w.each(o.stylesheets, (e) => {
                    d[e] ||
                      ((a = new f('link', 1)).attr({
                        rel: 'stylesheet',
                        text: 'text/css',
                        href: e,
                      }),
                      (a.shortEnded = !0),
                      r(a)),
                      delete d[e];
                  }),
                  w.each(d, (e) => {
                    e.remove();
                  }),
                  (a = c.getAll('body')[0]) &&
                    (n(a, 'dir', o.langdir),
                    n(a, 'style', o.style),
                    n(a, 'vlink', o.visited_color),
                    n(a, 'link', o.link_color),
                    n(a, 'alink', o.active_color),
                    i.setAttribs(e.getBody(), {
                      style: o.style,
                      dir: o.dir,
                      vLink: o.visited_color,
                      link: o.link_color,
                      aLink: o.active_color,
                    })),
                  (a = c.getAll('html')[0]) &&
                    (n(a, 'lang', o.langcode), n(a, 'xml:lang', o.langcode)),
                  s.firstChild || s.remove();
                var u = p({
                  validate: !1,
                  indent: !0,
                  indent_before: 'head,html,body,meta,title,script,link,style',
                  indent_after: 'head,html,body,meta,title,script,link,style',
                }).serialize(c);
                return u.substring(0, u.indexOf('</body>'));
              })(l, w.extend(o, t), i.get());
            i.set(n), e.close();
          },
        });
    });
  }
  function A(e) {
    return e.replace(/<\/?[A-Z]+/g, (e) => e.toLowerCase());
  }
  function s(e, t, n, l) {
    var i,
      o,
      r,
      a,
      c,
      s,
      d,
      u,
      m,
      g,
      f,
      p,
      h,
      y = '',
      v = e.dom;
    l.selection ||
      ((a = e.getParam('protect')),
      (c = l.content),
      w.each(a, (e) => {
        c = c.replace(e, (e) => '\x3c!--mce:protected ' + escape(e) + '--\x3e');
      }),
      (r = c),
      (l.format === 'raw' && t.get()) ||
        (l.source_view && _(e)) ||
        ((i = (r = (r =
          r.length !== 0 || l.source_view
            ? r
            : w.trim(t.get()) +
              '\n' +
              w.trim(r) +
              '\n' +
              w.trim(n.get())).replace(/<(\/?)BODY/gi, '<$1body')).indexOf(
          '<body'
        )) !== -1
          ? ((i = r.indexOf('>', i)),
            t.set(A(r.substring(0, i + 1))),
            (o = r.indexOf('</body', i)) === -1 && (o = r.length),
            (l.content = w.trim(r.substring(i + 1, o))),
            n.set(A(r.substring(o))))
          : (t.set(
              ((h = p = ''),
              (g = e).getParam('fullpage_default_xml_pi') &&
                (p +=
                  '<?xml version="1.0" encoding="' +
                  (b(g) || 'ISO-8859-1') +
                  '" ?>\n'),
              (p += g.getParam('fullpage_default_doctype', '<!DOCTYPE html>')),
              (p += '\n<html>\n<head>\n'),
              (f = g.getParam('fullpage_default_title')) &&
                (p += '<title>' + f + '</title>\n'),
              (f = b(g)) &&
                (p +=
                  '<meta http-equiv="Content-Type" content="text/html; charset=' +
                  f +
                  '" />\n'),
              (f = x(g)) && (h += 'font-family: ' + f + ';'),
              (f = k(g)) && (h += 'font-size: ' + f + ';'),
              (f = g.getParam('fullpage_default_text_color')) &&
                (h += 'color: ' + f + ';'),
              (p += '</head>\n<body' + (h ? ' style="' + h + '"' : '') + '>\n'))
            ),
            n.set('\n</body>\n</html>')),
        (s = C(e, t.get())),
        P(s.getAll('style'), (e) => {
          e.firstChild && (y += e.firstChild.value);
        }),
        (d = s.getAll('body')[0]) &&
          v.setAttribs(e.getBody(), {
            style: d.attr('style') || '',
            dir: d.attr('dir') || '',
            vLink: d.attr('vlink') || '',
            link: d.attr('link') || '',
            aLink: d.attr('alink') || '',
          }),
        v.remove('fullpage_styles'),
        (u = e.getDoc().getElementsByTagName('head')[0]),
        y &&
          v
            .add(u, 'style', { id: 'fullpage_styles' })
            .appendChild(document.createTextNode(y)),
        (m = {}),
        w.each(u.getElementsByTagName('link'), (e) => {
          e.rel === 'stylesheet' &&
            e.getAttribute('data-mce-fullpage') &&
            (m[e.href] = e);
        }),
        w.each(s.getAll('link'), (e) => {
          var t = e.attr('href');
          if (!t) return !0;
          m[t] ||
            e.attr('rel') !== 'stylesheet' ||
            v.add(u, 'link', {
              rel: 'stylesheet',
              text: 'text/css',
              href: t,
              'data-mce-fullpage': '1',
            }),
            delete m[t];
        }),
        w.each(m, (e) => {
          e.parentNode.removeChild(e);
        })));
  }
  var e = tinymce.util.Tools.resolve('tinymce.PluginManager'),
    g = function () {
      return (g =
        Object.assign ||
        ((e) => {
          for (var t, n = 1, l = arguments.length; n < l; n++)
            for (var i in (t = arguments[n]))
              Object.hasOwn(t, i) && (e[i] = t[i]);
          return e;
        })).apply(this, arguments);
    },
    w = tinymce.util.Tools.resolve('tinymce.util.Tools'),
    n = tinymce.util.Tools.resolve('tinymce.html.DomParser'),
    f = tinymce.util.Tools.resolve('tinymce.html.Node'),
    p = tinymce.util.Tools.resolve('tinymce.html.Serializer'),
    P = w.each;
  e.add('fullpage', (e) => {
    var t,
      o,
      r,
      a,
      n = i(''),
      l = i('');
    c(e, n),
      (t = e).ui.registry.addButton('fullpage', {
        tooltip: 'Metadata and document properties',
        icon: 'document-properties',
        onAction() {
          t.execCommand('mceFullPageProperties');
        },
      }),
      t.ui.registry.addMenuItem('fullpage', {
        text: 'Metadata and document properties',
        icon: 'document-properties',
        onAction() {
          t.execCommand('mceFullPageProperties');
        },
      }),
      (r = n),
      (a = l),
      (o = e).on('BeforeSetContent', (e) => {
        s(o, r, a, e);
      }),
      o.on('GetContent', (e) => {
        var t, n, l, i;
        (t = o),
          (n = r.get()),
          (l = a.get()),
          (i = e).format !== 'html' ||
            i.selection ||
            (i.source_view && _(t)) ||
            (i.content = (
              w.trim(n) +
              '\n' +
              w.trim(i.content) +
              '\n' +
              w.trim(l)
            ).replace(/<!--mce:protected ([\s\S]*?)-->/g, (e, t) =>
              unescape(t)
            ));
      });
  });
})();
