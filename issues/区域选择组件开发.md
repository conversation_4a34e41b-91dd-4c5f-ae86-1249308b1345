# 区域选择组件开发任务

## 任务概述
开发一个区域选择组件，放在 `src/app/pbl/record/components` 下面，数据使用模拟数据。参考 `src/app/pbl/material/create/page.tsx` 里的"观察地点选择 Popup"。

## 实现方案
基于 `TimeSelector` 组件的设计模式，创建简化版区域选择组件。

## 已完成的工作

### 1. 组件结构创建 ✅
```
src/app/pbl/record/components/RegionSelector/
├── index.tsx          # 主组件文件
├── types.ts           # TypeScript 类型定义
└── mockData.ts        # 模拟数据文件
```

### 2. 类型定义实现 ✅
- `RegionOption` 接口：单个区域选项
- `RegionCategory` 接口：区域分类
- `RegionSelectorProps` 接口：组件属性
- `SelectedRegion` 接口：选中的区域信息

### 3. 模拟数据实现 ✅
基于 `material/create/page.tsx` 中的 `regionData`，包含：
- **室内区域**：活动室、教室、洗漱区、走廊、楼梯、游戏室、666、小山坡
- **区角区域**：科学区、美工区、阅读区、建构区、益智区、生活区、语言区
- **户外区域**：公共区域、操场、攀爬区

### 4. 主组件实现 ✅
**核心功能**：
- 触发器显示当前选中区域
- Popup 弹窗展示区域选择界面
- 分类展示（室内、区角、户外）
- 单选逻辑，支持取消选中
- 回调函数处理选择结果

**技术特点**：
- 基于 `TimeSelector` 的设计模式
- 使用 `useCallback` 和 `useMemo` 性能优化
- 完整的 TypeScript 类型安全
- antd-mobile + Tailwind CSS 样式

### 5. 页面集成 ✅
在 `src/app/pbl/record/class/page.tsx` 中：
- 导入 `RegionSelector` 组件和相关类型
- 添加区域选择状态管理
- 替换原有的区域选择触发器
- 实现区域选择回调处理

## 组件使用方法

```typescript
import RegionSelector, { type SelectedRegion } from '../components/RegionSelector';

// 状态管理
const [selectedRegion, setSelectedRegion] = useState<SelectedRegion | null>(null);

// 回调处理
const handleRegionConfirm = (region: SelectedRegion | null) => {
  setSelectedRegion(region);
  console.log('选择的区域:', region);
};

// 组件使用
<RegionSelector 
  onConfirm={handleRegionConfirm}
  defaultRegion={selectedRegion}
  className="cursor-pointer"
  placeholder="全部区域"
/>
```

## 组件特性

### 用户体验
- **简洁直观**：点击触发器打开区域选择弹窗
- **分类清晰**：三个主要分类，每个分类下的区域一目了然
- **操作便捷**：单选模式，点击即选中，再次点击可取消
- **反馈及时**：选中状态有明显的视觉反馈

### 技术特点
- **类型安全**：完整的 TypeScript 类型定义
- **性能优化**：使用 useCallback 和 useMemo 优化渲染
- **代码复用**：基于成熟的 TimeSelector 设计模式
- **易于维护**：清晰的文件结构和代码组织

## 任务状态
✅ **已完成** - 区域选择组件开发完成并成功集成到目标页面

## 测试建议
1. 测试区域选择功能
2. 验证取消选中功能
3. 确认UI表现和交互体验
4. 检查移动端适配效果
