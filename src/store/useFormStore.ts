import type { SchemaBase } from 'form-render-mobile';
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

type ItemType = SchemaBase & { id: string };

type BaseForm = {
  instanceName: string;
  iconUrl: string;
  remark: string;
  cateId: string;
};

type SettingForm = {
  cycleType: number;
  cycleTime: string[];
  isRepeat: number;
  isHoliday: number;
  endTime: number;
  remindTime: [number, number, number, number];
  isCustom: boolean;
  scope: Array<{
    id: string;
    type: string;
    name?: string;
  }>;
};

type User = {
  id: string;
  name: string;
  avatar: string;
};

type State = {
  baseForm: BaseForm;
  settingForm: SettingForm;
  forms: ItemType[];
  users: Record<string, User>;
  widgetId: string; // 用于存储当前编辑的组件ID
};

type Actions = {
  setBaseForm: (data: BaseForm) => void;
  setSettingForm: (data: SettingForm) => void;
  setForm: (data: ItemType[]) => void;
  addFormItem: (item: ItemType) => void;
  updateFormItem: (id: string, item: ItemType) => void;
  removeFormItem: (id: string) => void;
  addUser: (user: User) => void;
  getUser: (id: string) => User | undefined;
  setWidgetId: (id: string) => void;
  // 分组相关方法
  addGroupChildItem: (groupId: string, item: ItemType) => void;
  removeGroupChildItem: (groupId: string, childId: string) => void;
  updateGroupChildItem: (
    groupId: string,
    childId: string,
    item: ItemType
  ) => void;
};

export const useFormStore = create<State & Actions>()(
  immer((set, get) => ({
    baseForm: {
      instanceName: '',
      iconUrl: '',
      remark: '',
      cateId: '',
    },
    settingForm: {
      cycleType: 0,
      cycleTime: ['1', '2', '3', '4', '5'],
      isRepeat: 0,
      isHoliday: 0,
      endTime: 0,
      remindTime: [0, 0, 0, 0],
      isCustom: false,
      scope: [],
    },
    forms: [],
    setBaseForm: (data) =>
      set((state) => {
        state.baseForm = data;
      }),
    setSettingForm: (data) =>
      set((state) => {
        state.settingForm = data;
      }),
    setForm: (data) =>
      set((state) => {
        state.forms = data;
      }),
    addFormItem: (item: ItemType) =>
      set((state) => {
        state.forms.push(item);
      }),
    updateFormItem: (id, item) => {
      set((state) => {
        const index = state.forms.findIndex((form) => form.id === id);
        if (index > -1) {
          state.forms[index] = item;
        }
      });
    },
    removeFormItem: (id) => {
      set((state) => {
        const index = state.forms.findIndex((form) => form.id === id);
        if (index > -1) {
          state.forms.splice(index, 1);
        }
      });
    },
    users: {},
    addUser: (user) =>
      set((state) => {
        state.users[user.id] = user;
      }),
    getUser: (id) => {
      const state = get();
      return state.users[id];
    },
    widgetId: '',
    setWidgetId: (id) => {
      set((state) => {
        state.widgetId = id;
      });
    },
    // 分组相关方法实现
    addGroupChildItem: (groupId, item) => {
      set((state) => {
        const groupIndex = state.forms.findIndex((form) => form.id === groupId);
        if (groupIndex > -1) {
          const groupForm = state.forms[groupIndex];
          if (groupForm && groupForm.widget === 'group') {
            if (!groupForm.properties) {
              groupForm.properties = {};
            }
            groupForm.properties[item.id] = item;
          }
        }
      });
    },
    removeGroupChildItem: (groupId, childId) => {
      set((state) => {
        const groupIndex = state.forms.findIndex((form) => form.id === groupId);
        if (groupIndex > -1) {
          const groupForm = state.forms[groupIndex];
          if (
            groupForm &&
            groupForm.widget === 'group' &&
            groupForm.properties
          ) {
            delete groupForm.properties[childId];
          }
        }
      });
    },
    updateGroupChildItem: (groupId, childId, item) => {
      set((state) => {
        const groupIndex = state.forms.findIndex((form) => form.id === groupId);
        if (groupIndex > -1) {
          const groupForm = state.forms[groupIndex];
          if (
            groupForm &&
            groupForm.widget === 'group' &&
            groupForm.properties
          ) {
            groupForm.properties[childId] = item;
          }
        }
      });
    },
  }))
);
