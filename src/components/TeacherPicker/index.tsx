import { Button, Checkbox, List, Popup, SearchBar, Toast } from 'antd-mobile';
import Image from 'next/image';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import { useImmer } from 'use-immer';

import { getDeptUser, searchTeacher } from '@/api/common';
import { PiCaretRightBold, PiMinusCircleFill } from '@/components/Icons';
import { defaultAvatar, departmentAvatar } from '@/constant/config';

export interface TeacherPickerRef {
  toggle(): void;
  initData(data: any[], isSelectDept: boolean): void;
}
interface Props {
  title: string;
  onConfirm: (data: any[]) => void;
}

type TreeNode = {
  id: string;
  name: string;
  level: number;
  children?: TreeNode[];
  staffList?: Array<{
    id: string;
    name: string;
    avatar: string;
  }>;
};

const TeacherPicker = forwardRef<TeacherPickerRef, Props>((props, ref) => {
  const { onConfirm, title } = props;
  const [data, setData] = useState<any[]>([]); // 所有数据
  const [value, setValue] = useState<TreeNode>(); // 当前页面显示的数据
  const [popupVisible, setPopupVisible] = useState(false);
  const [selectedData, setSelectedData] = useState<any[]>([]);

  const [breadcrumb, setBreadcrumb] = useImmer<TreeNode[]>([]);
  const [canSelectDept, setCanSelectDept] = useState<boolean>(false);
  const [keyword, setKeyword] = useState('');
  const [searchList, setSearchList] = useState<any[]>([]);

  useImperativeHandle(ref, () => ({
    toggle() {
      setPopupVisible((prev) => !prev);
    },
    initData(data, isSelectDept) {
      setSelectedData(data);
      setCanSelectDept(isSelectDept);
    },
  }));

  useEffect(() => {
    initData();
  }, []);

  const initData = () => {
    getDeptUser({ parentId: 0, fetchChild: 1 }).then((res: any) => {
      setData(res);
      setValue(res);
      setBreadcrumb([{ ...res }]);
    });
  };

  const search = () => {
    if (keyword !== '') {
      searchTeacher(keyword).then((res: any) => {
        if (Array.isArray(res.list) && res.list.length) {
          setSearchList(res.list);
        } else {
          Toast.show('暂无你搜索的教师');
        }
      });
    }
  };

  const getTreeNodeData = (id: string) => {
    const newData = JSON.parse(JSON.stringify(data));
    const result = findInTree(newData, (node) => node.id === id);
    if (result) {
      setValue(result);
    }
  };

  const findInTree = (
    tree: TreeNode,
    predicate: (data: TreeNode) => boolean
  ): any => {
    // 如果当前节点满足条件，返回当前节点
    if (predicate(tree)) {
      console.log('🚀 ~ file: index.tsx:1512 ~ tree:', tree);
      setBreadcrumb((draft) => {
        const index = draft.findIndex((item) => item.id === tree.id);
        if (index === -1) {
          draft.push(tree);
        } else {
          draft.splice(index + 1);
        }
      });
      return tree;
    }

    // 如果当前节点有子节点，递归查找子节点
    if (Array.isArray(tree.children) && tree.children.length > 0) {
      for (const child of tree.children) {
        const result = findInTree(child, predicate);
        if (result) {
          return result;
        }
      }
    }
  };

  const submit = () => {
    setPopupVisible(false);
    onConfirm(selectedData);
    setSelectedData([]);
  };

  return (
    <Popup
      bodyStyle={{
        borderTopLeftRadius: '8px',
        borderTopRightRadius: '8px',
        minHeight: '56vh',
        maxHeight: '100vh',
        paddingTop: '10px',
      }}
      onMaskClick={() => {
        setPopupVisible(false);
      }}
      visible={popupVisible}
    >
      <div className="p-4 pt-2">
        <div className="mb-4 flex items-center justify-between text-base">
          <div
            className="text-stone-400"
            onClick={() => setPopupVisible(false)}
          >
            取消
          </div>
          <div className="text-lg text-stone-900">{title}</div>
          <div className="text-[#3B82F7]" onClick={() => submit()}>
            确定
          </div>
        </div>
        <div className="mb-4 flex items-center">
          <div className="flex-1">
            <SearchBar
              onChange={setKeyword}
              onClear={() => {
                setKeyword('');
                setSearchList([]);
              }}
              style={{
                '--border-radius': '100px',
              }}
            />
          </div>
          {keyword !== '' && (
            <div className="ml-2">
              <Button
                color="primary"
                // shape="rounded"
                onClick={() => {
                  search();
                }}
                size="small"
              >
                搜索
              </Button>
            </div>
          )}
        </div>
        <div className="flex flex-wrap">
          {selectedData.map((item) => (
            <div
              className="relative mb-4 flex w-1/5 flex-col items-center"
              key={item.id}
            >
              <Image
                alt=""
                className="h-[100px] w-[100px] rounded-full object-cover"
                height="0"
                sizes="100px"
                src={item.avatar || defaultAvatar}
                width="0"
              />
              <span className="mt-1 w-[120px] truncate text-center">
                {item.name || '无名字'}
              </span>
              <div
                className="-top-2 absolute right-1 rounded-full bg-white"
                onClick={() => {
                  setSelectedData((prev) =>
                    prev.filter((selectedItem) => selectedItem.id !== item.id)
                  );
                }}
              >
                <PiMinusCircleFill color="#FF6767" size="18px" />
              </div>
            </div>
          ))}
        </div>
        {searchList.length ? (
          <div className="flex max-h-[50vh] flex-col overflow-y-scroll">
            <List
              style={{
                // '--border-bottom': 'none',
                '--border-top': 'none',
                '--border-inner': 'solid 1px #F1F1F1',
              }}
            >
              {searchList?.map((item) => {
                return (
                  <List.Item
                    arrow={false}
                    key={item.id}
                    prefix={
                      <div onClick={(e) => e.stopPropagation()}>
                        <Checkbox
                          checked={selectedData.some(
                            (selectedItem) => selectedItem.id === item.id
                          )}
                          key={item.id}
                          onChange={(checked) => {
                            if (checked) {
                              setSelectedData((prev) => [
                                ...prev,
                                {
                                  id: item.id,
                                  name: item.name,
                                  avatar: item.avatar || defaultAvatar,
                                  type: 'user',
                                },
                              ]);
                            } else {
                              setSelectedData((prev) =>
                                prev.filter(
                                  (selectedItem) => selectedItem.id !== item.id
                                )
                              );
                            }
                          }}
                        />
                      </div>
                    }
                  >
                    <div className="flex flex-1 justify-between">
                      <div>{item.name}</div>
                    </div>
                  </List.Item>
                );
              })}
            </List>
          </div>
        ) : (
          <>
            <div className="mb-3 flex items-center">
              {breadcrumb.map((item, index) => {
                return (
                  <div
                    className="flex items-center"
                    key={item.id}
                    onClick={() => {
                      getTreeNodeData(item.id);
                    }}
                  >
                    <span
                      className="mr-1 text-base"
                      key={item.id}
                      onClick={() => {
                        getTreeNodeData(item.id);
                      }}
                    >
                      {item.name}
                    </span>
                    <PiCaretRightBold color="#D8D8D8" size="18px" />
                  </div>
                );
              })}
            </div>
            <div className="flex max-h-[50vh] flex-col overflow-y-scroll">
              <List
                style={{
                  // '--border-bottom': 'none',
                  '--border-top': 'none',
                  '--border-inner': 'solid 1px #F1F1F1',
                }}
              >
                {value &&
                  Array.isArray(value.children) &&
                  value.children?.map((item) => {
                    return (
                      <List.Item
                        arrow={false}
                        key={item.id}
                        prefix={
                          canSelectDept ? (
                            <div onClick={(e) => e.stopPropagation()}>
                              <Checkbox
                                checked={selectedData.some(
                                  (selectedItem) => selectedItem.id === item.id
                                )}
                                key={item.id}
                                onChange={(checked) => {
                                  if (checked) {
                                    setSelectedData((prev) => [
                                      ...prev,
                                      {
                                        id: item.id,
                                        name: item.name,
                                        avatar: departmentAvatar,
                                        type: 'dept',
                                      },
                                    ]);
                                  } else {
                                    setSelectedData((prev) =>
                                      prev.filter(
                                        (selectedItem) =>
                                          selectedItem.id !== item.id
                                      )
                                    );
                                  }
                                }}
                              />
                            </div>
                          ) : null
                        }
                      >
                        <div className="flex flex-1 justify-between">
                          <div>{item.name}</div>
                          {((item.children && item.children.length > 0) ||
                            (item.staffList && item.staffList.length > 0)) && (
                            <div
                              className="flex items-center"
                              onClick={() => {
                                getTreeNodeData(item.id);
                              }}
                            >
                              <span className="mr-1 text-[#4E78FF]">下级</span>
                              <PiCaretRightBold color="#D8D8D8" size="18px" />
                            </div>
                          )}
                        </div>
                      </List.Item>
                    );
                  })}
                {value &&
                  Array.isArray(value.staffList) &&
                  value.staffList?.map((item) => {
                    return (
                      <List.Item
                        arrow={false}
                        key={item.id}
                        prefix={
                          <div onClick={(e) => e.stopPropagation()}>
                            <Checkbox
                              checked={selectedData.some(
                                (selectedItem) => selectedItem.id === item.id
                              )}
                              key={item.id}
                              onChange={(checked) => {
                                if (checked) {
                                  setSelectedData((prev) => [
                                    ...prev,
                                    {
                                      id: item.id,
                                      name: item.name,
                                      avatar: item.avatar,
                                      type: 'user',
                                    },
                                  ]);
                                } else {
                                  setSelectedData((prev) =>
                                    prev.filter(
                                      (selectedItem) =>
                                        selectedItem.id !== item.id
                                    )
                                  );
                                }
                              }}
                            />
                          </div>
                        }
                      >
                        <div className="flex flex-1 justify-between">
                          <div>{item.name}</div>
                        </div>
                      </List.Item>
                    );
                  })}
              </List>
            </div>
          </>
        )}
      </div>
    </Popup>
  );
});

export default TeacherPicker;
