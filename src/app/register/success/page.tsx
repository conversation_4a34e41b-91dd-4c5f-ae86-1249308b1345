import Image from 'next/image';
import * as React from 'react';

import { PiCheckCircleFill } from '@/components/Icons';
import Layout from '@/components/layout/Layout';
import { isPalmBaby } from '@/lib/utils';

import Download from './Download';

export default function SuccessPage({ searchParams }) {
  const { mobile, password } = searchParams;
  const isPalmBabyApp =
    typeof window !== 'undefined'
      ? isPalmBaby(window.location.hostname)
      : false;
  return (
    <Layout>
      <main className="h-screen">
        <Image
          alt=""
          className="absolute z-0 h-screen w-full object-cover"
          height="0"
          sizes="100vw"
          src="/images/invite/inviteBG.png"
          width="0"
        />
        <section className="relative z-10 flex h-full flex-col items-center ">
          <div
            className="relative mt-8 flex items-center justify-center"
            style={{ width: '60px', height: '60px', borderRadius: '100%' }}
          >
            <div
              className="absolute inset-0 bg-white"
              style={{
                width: '30px',
                height: '30px',
                borderRadius: '100%',
                zIndex: 0,
                top: '15px',
                left: '15px',
              }}
            />
            <PiCheckCircleFill
              color={isPalmBabyApp ? '#17C5A6' : '#4e78ff'}
              fontSize={60}
              style={{ zIndex: 1 }}
            />
          </div>
          <div className="py-5 text-[#333] text-lg">
            恭喜您，已经成功创建幼儿园
          </div>
          <div className="mt-[20px] w-3/5 rounded-[16px] bg-[#FFFFFF6A] px-[70px] py-[25px] text-[#333]">
            <p className="mb-2">登录手机号: {mobile}</p>
            <p>登录密码: {password}</p>
          </div>
          <p className="mt-4 px-4 text-left text-[#333] text-sm">
            (提示:若前面已经创建了园所,则密码还是使用原来的密码)
          </p>
          <Download />
        </section>
      </main>
    </Layout>
  );
}
