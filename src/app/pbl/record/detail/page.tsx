'use client';

import { memo, useEffect, useRef, useState } from 'react';
import {
  Edit3,
  Share2,
  Trash2,
  Users,
  Calendar,
  ClipboardList,
  Image,
  CalendarRange,
  MessageSquare,
  ChevronDown,
  ChevronUp,
  RefreshCw,
  MapPin,
  Tag
} from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  getObservationDetail,
  deleteObservation,
  updateObservation
} from '@/api/pbl';
import EditTextDialog from '../detail/day/components/EditTextDialog';
import StudentPicker, {
  type StudentPickerRef
} from '@/components/StudentPicker';
import { Plus } from 'lucide-react';
import Media from '../detail/components/Media';
import StudentCard from './components/StudentCard';
import { Dialog, SpinLoading, Toast } from 'antd-mobile';
import { useSetAtom } from 'jotai';
import { studentAtom, mediaAtom } from '@/store/pbl';
import { share } from '@/utils/index';
import { useImmer } from 'use-immer';

interface AbilityTarget {
  id: number;
  name: string;
  completed: boolean;
}

interface AbilityCategory {
  id: string;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  targets: AbilityTarget[];
  subCategories?: {
    name: string;
    targets: AbilityTarget[];
  }[];
}

interface Student {
  id: string;
  name: string;
  avatar: string;
  abilities: string[];
  abilityCategories?: AbilityCategory[];
  progress?: string;
  evaluation: {
    observationId: string;
    abilities: {
      abilityId: string;
    }[];
  };
}

interface AssociatedStudentsProps {
  students: Student[];
  deptId: string;
  observationId: string;
}

const AssociatedStudents = memo(
  ({ students, deptId, observationId }: AssociatedStudentsProps) => {
    console.log('🚀 ~ students:', students);
    const router = useRouter();
    const [expandedStudents, setExpandedStudents] = useState<number[]>([]);
    const studentPickerRef = useRef<StudentPickerRef>(null);
    const setStudent = useSetAtom(studentAtom);

    const handleSelectStudent = async () => {
      if (studentPickerRef.current) {
        try {
          const selectedStudent =
            await studentPickerRef.current.selectStudentsByClassId();
          if (selectedStudent) {
            console.log('选中的学生：', selectedStudent);
            if (
              students.some(
                (student) => student.id === selectedStudent.studentId
              )
            ) {
              Toast.show({
                content: '学生已存在',
                position: 'bottom'
              });
              return;
            }
            setStudent({
              id: selectedStudent.studentId,
              name: selectedStudent.studentName,
              avatar: selectedStudent.avatar,
              deptId: deptId,
              evaluation: {
                evaluationId: '',
                observationId: observationId,
                abilities: []
              }
            });
            router.push('/pbl/record/update/evaluation');
          }
        } catch (error) {
          console.error('选择学生失败：', error);
        }
      }
    };

    // 切换学生卡片展开/折叠状态
    const toggleStudentExpand = (studentId: number, e: React.MouseEvent) => {
      e.stopPropagation(); // 阻止事件冒泡，避免触发卡片点击事件
      setExpandedStudents((prev) =>
        prev.includes(studentId)
          ? prev.filter((id) => id !== studentId)
          : [...prev, studentId]
      );
    };

    // 检查学生是否展开
    const isStudentExpanded = (studentId: number) =>
      expandedStudents.includes(studentId);

    return (
      <div className="px-4 mb-6">
        <h3 className="text-md font-bold mb-2 flex justify-between items-center gap-2">
          <div className="flex items-center gap-2">
            <Users className="w-5 h-5 text-indigo-500" />
            <span>关联学生</span>
          </div>
          <div>
            <button
              type="button"
              className="flex items-center text-xs gap-1 bg-indigo-50 text-indigo-600 hover:bg-indigo-100 transition-colors px-3 py-1.5 rounded-full"
              onClick={() => handleSelectStudent()}
            >
              <Plus className="w-4 h-4" /> 新增学生
            </button>
          </div>
        </h3>
        <div className="space-y-3">
          {students.map((student) => (
            <StudentCard
              key={student.id}
              student={student}
              toggleStudentExpand={toggleStudentExpand}
              isStudentExpanded={
                students.length === 1 ? () => true : isStudentExpanded
              }
            />
          ))}
        </div>
        <StudentPicker ref={studentPickerRef} classId={deptId} hideUI={true} />
      </div>
    );
  }
);

function FooterActions({ observationId }: { observationId: string }) {
  const router = useRouter();

  const handleRegenerate = () => {
    Dialog.alert({
      title: '重新生成观察记录',
      content: (
        <div>
          <p className="text-red-400 mb-2">
            对内容不满意，可以补充信息重新生成新的观察记录，重新生成将会删除当前的记录，并重新生成新的观察内容和学生能力评估，请谨慎操作。
          </p>
          <textarea
            className="border border-gray-300 p-2 rounded w-full"
            rows={5}
            placeholder="请输入你的补充信息，信息越详细，AI生成的内容越精确"
          />
        </div>
      ),
      confirmText: '确认重新生成'
    });
  };

  const handleDelete = () => {
    Dialog.confirm({
      content: '删除后记录将无法恢复',
      cancelText: '取消',
      confirmText: <div className="text-red-400">确认删除</div>,
      onConfirm: async () => {
        await deleteObservation(observationId);
        Toast.show({
          icon: 'success',
          content: '删除成功',
          position: 'bottom'
        });
        router.back();
      }
    });
  };

  return (
    <div className="sticky bottom-0">
      <div className="flex justify-around items-center bg-card p-2 rounded-2xl shadow-md">
        {/* <button
          type="button"
          className="flex flex-col items-center text-muted-foreground hover:text-primary transition-colors p-2 rounded-lg"
          onClick={() => {
            router.push(`/pbl/record/create?observationId=${observationId}`);
          }}
        >
          <Edit3 className="w-5 h-5 mb-1" />
          <span className="text-xs">编辑</span>
        </button> */}
        <button
          type="button"
          className="flex flex-col items-center text-muted-foreground hover:text-primary transition-colors p-2 rounded-lg"
          onClick={() => {
            const shareData = {
              type: 0,
              title: '老师分享了新的观察记录',
              description: '',
              thumbImage: '',
              url: `${window.location.href}`
            };
            share(shareData);
          }}
        >
          <Share2 className="w-5 h-5 mb-1" />
          <span className="text-xs">分享</span>
        </button>
        <button
          type="button"
          className="flex flex-col items-center text-primary hover:text-primary-700 transition-colors p-2 rounded-lg"
          onClick={handleRegenerate}
        >
          <RefreshCw className="w-5 h-5 mb-1" />
          <span className="text-xs">重新生成</span>
        </button>
        <button
          type="button"
          className="flex flex-col items-center text-red-500 hover:text-red-700 transition-colors p-2 rounded-lg"
          onClick={handleDelete}
        >
          <Trash2 className="w-5 h-5 mb-1" />
          <span className="text-xs">删除</span>
        </button>
      </div>
    </div>
  );
}

interface ConversationItem {
  speaker: string;
  text: string;
}

export default function App() {
  const searchParams = useSearchParams();
  const observationId = searchParams?.get('observationId');
  const [isExpanded, setIsExpanded] = useState(false);

  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [record, setRecord] = useImmer({
    date: '',
    content: '',
    nextStepPlan: '',
    students: [],
    medias: [],
    createUser: {
      name: '',
      avatar: ''
    },
    createTime: '',
    deptId: '',
    conversation: [],
    observationLocation: '教室角落',
    tags: ['自由游戏', '小组合作']
  });

  const [isEditRecordDialogVisible, setIsEditRecordDialogVisible] =
    useState(false);
  const [isEditSuggestDialogVisible, setIsEditSuggestDialogVisible] =
    useState(false);
  const setMedia = useSetAtom(mediaAtom);

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '观察记录详情';
    }
  }, []);

  useEffect(() => {
    if (observationId) {
      setLoading(true);
      getObservationDetail(observationId)
        .then((res) => {
          setRecord(res);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [observationId]);

  const onRecordUpdate = async (text: string | undefined) => {
    if (!text) return;
    if (!observationId) return;
    await updateObservation(observationId, {
      title: '',
      content: text,
      nextStepPlan: record?.nextStepPlan,
      observationId: observationId
    });
    Toast.show({
      content: '更新成功'
    });
  };

  const onSuggestUpdate = async (text: string | undefined) => {
    if (!text) return;
    if (!observationId) return;

    await updateObservation(observationId, {
      title: '',
      nextStepPlan: text,
      content: record?.content,
      observationId: observationId
    });
    Toast.show({
      content: '更新成功'
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <SpinLoading color="primary" />
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col relative">
      {/* 可滚动内容区域 */}
      <div className="flex-grow overflow-y-auto pb-20">
        <div className="px-4 my-6">
          <div className="flex items-center mb-6 gap-2">
            <div className="flex items-center">
              <img
                src={record.createUser?.avatar || ''}
                alt="老师头像"
                className="w-10 h-10 rounded-full"
              />
            </div>
            <div>
              <span className="text-base">{record.createUser?.name}</span>
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <Calendar className="w-3 h-3" />
                <span>{record.createTime}</span>
              </div>
            </div>
          </div>
          <div className="mb-6 flex items-center">
            <h3 className="text-base font-bold text-gray-700 flex items-center gap-2">
              <MapPin className="w-5 h-5 text-green-500" />
              <span>观察地点：</span>
            </h3>
            <div className="text-gray-700 text-base">活动室</div>
          </div>
          {/* 观察记录内容 */}
          <div className="mb-6">
            <h3 className="text-base font-bold text-gray-700 mb-2 flex items-center gap-2">
              <ClipboardList className="w-5 h-5 text-amber-500" />
              <span>记录内容</span>
            </h3>
            <div className="text-gray-700 leading-relaxed text-base bg-amber-50 p-4 rounded-2xl">
              {record.content}
              <button
                type="button"
                className="inline-block p-1 text-gray-500 hover:text-primary transition-colors"
                onClick={() => setIsEditRecordDialogVisible(true)}
              >
                <Edit3 className="w-4 h-4 text-indigo-500" />
              </button>
            </div>
          </div>

          {/* 观察地点 */}
          <div className="mb-6">
            {['小组合作', '自由游戏', '标签3']?.map((tag, index) => {
              const tagColors = [
                'bg-green-50 text-green-700',
                'bg-orange-50 text-orange-700',
                'bg-purple-50 text-purple-700',
                'bg-blue-50 text-blue-700',
                'bg-pink-50 text-pink-700'
              ];
              return (
                <span
                  key={tag}
                  className={`px-3 py-1.5 mr-2 rounded-full text-sm font-medium ${
                    tagColors[index % tagColors.length]
                  }`}
                >
                  #{tag}
                </span>
              );
            })}
          </div>
        </div>
        <AssociatedStudents
          students={record.students || []}
          deptId={record.deptId}
          observationId={observationId || ''}
        />
        <div className="px-4">
          <h3 className="text-md font-bold text-gray-700 mb-3 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Image className="w-5 h-5 text-green-500" />
              <span>关联文件</span>
              <span className="text-xs text-gray-500 font-normal">
                ({record.medias.length})
              </span>
            </div>
            <div>
              <button
                type="button"
                className="flex items-center text-xs gap-1 bg-gray-50 text-gray-600 hover:bg-gray-100 transition-colors px-3 py-1.5 rounded-full"
                onClick={() => {
                  setMedia({
                    observationId: observationId || '',
                    deptId: record.deptId,
                    medias: record.medias.map((media: any) => ({
                      ...media,
                      status: 'completed',
                      id: media.mediaId,
                      type:
                        media.type === 1
                          ? 'image'
                          : media.type === 2
                            ? 'video'
                            : 'audio'
                    }))
                  });
                  router.push('/pbl/record/update/media');
                }}
              >
                {record.medias.length > 0 ? '修改文件' : '添加文件'}
              </button>
            </div>
          </h3>
          <Media media={record.medias} />
        </div>
        {/* 下一步计划 */}
        {!!record.nextStepPlan && (
          <div className="mb-6 px-4">
            <h2 className="flex items-center gap-2 text-base font-semibold mb-2">
              <CalendarRange className="w-4 h-4 text-teal-400" />
              下一步计划
            </h2>

            <div className="bg-teal-50 p-3 rounded-lg border border-teal-200">
              <div className="text-gray-700 text-sm whitespace-pre-line">
                {record.nextStepPlan}
                <button
                  type="button"
                  className="inline-block p-1 text-gray-500 hover:text-primary transition-colors"
                  onClick={() => setIsEditSuggestDialogVisible(true)}
                >
                  <Edit3 className="w-4 h-4 text-blue-500" />
                </button>
              </div>
            </div>
          </div>
        )}
        {!!record.conversation.length && (
          <div className="px-4 mb-6">
            <h3 className="text-md font-bold text-gray-700 mb-3 flex items-center gap-2">
              <MessageSquare className="w-5 h-5 text-blue-500" />
              <span>人物对话</span>
              <span className="text-xs text-gray-500 font-normal">
                ({record.conversation.length})
              </span>
            </h3>
            <div className="space-y-4">
              {(isExpanded
                ? record.conversation
                : record.conversation.slice(-5)
              ).map((item: ConversationItem, index: number) => {
                const isTeacher = item.speaker === '老师';
                return (
                  <div
                    key={`${item.speaker}-${index}`}
                    className={`flex ${isTeacher ? 'justify-start' : 'justify-end'}`}
                  >
                    <div className="max-w-[85%]">
                      <div
                        className={`flex items-center gap-2 mb-1 ${isTeacher ? '' : 'justify-end'}`}
                      >
                        <div
                          className={`py-0.5 rounded text-sm font-bold
                          ${isTeacher ? ' text-blue-700' : ' text-green-700'}`}
                        >
                          {item.speaker}
                        </div>
                      </div>
                      <div
                        className={`p-3 rounded-2xl break-words
                        ${
                          isTeacher
                            ? 'bg-blue-50 border border-blue-100 rounded-tl-none'
                            : 'bg-green-50 border border-green-100 rounded-tr-none'
                        }`}
                      >
                        <p className="text-sm whitespace-pre-line leading-relaxed">
                          {item.text}
                        </p>
                      </div>
                    </div>
                  </div>
                );
              })}
              {record.conversation.length > 5 &&
                (isExpanded ? (
                  <button
                    type="button"
                    onClick={() => setIsExpanded(false)}
                    className="w-full py-2 px-4 text-sm text-gray-500 hover:text-gray-700 flex items-center justify-center gap-1 bg-gray-50 rounded-lg transition-colors"
                  >
                    <ChevronUp className="w-4 h-4" />
                    收起对话
                  </button>
                ) : (
                  <button
                    type="button"
                    onClick={() => setIsExpanded(true)}
                    className="w-full py-2 px-4 text-sm text-gray-500 hover:text-gray-700 flex items-center justify-center gap-1 bg-gray-50 rounded-lg transition-colors"
                  >
                    <ChevronDown className="w-4 h-4" />
                    显示全部 {record.conversation.length} 条对话
                  </button>
                ))}
            </div>
          </div>
        )}
      </div>
      <FooterActions observationId={observationId || ''} />
      {/* 编辑记录内容对话框 */}
      <EditTextDialog
        visible={isEditRecordDialogVisible}
        onClose={() => setIsEditRecordDialogVisible(false)}
        initialContent={record.content}
        onSave={async (content) => {
          await onRecordUpdate(content);
        }}
        onSuccess={(newContent) => {
          setRecord((draft) => {
            draft.content = newContent;
          });
        }}
        title="编辑记录内容"
        placeholder="请输入记录内容"
        emptyMessage="记录内容不能为空"
      />

      {/* 编辑下一步计划对话框 */}
      <EditTextDialog
        visible={isEditSuggestDialogVisible}
        onClose={() => setIsEditSuggestDialogVisible(false)}
        initialContent={record.nextStepPlan}
        onSave={async (content) => {
          await onSuggestUpdate(content);
        }}
        onSuccess={(newContent) => {
          setRecord((draft) => {
            draft.nextStepPlan = newContent;
          });
        }}
        title="编辑下一步计划"
        placeholder="请输入下一步计划"
        emptyMessage="下一步计划不能为空"
      />
    </div>
  );
}
