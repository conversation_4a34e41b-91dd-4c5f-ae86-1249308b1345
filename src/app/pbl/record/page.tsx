'use client';

import {
  <PERSON><PERSON><PERSON><PERSON>heck,
  FileText,
  Compass,
  Library,
  Settings,
  Users
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { getObservationClassList } from '@/api/pbl';
import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';
import TimeSelector, { type TimeRange } from './components/TimeSelector';

// 定义班级数据类型
interface ClassItem {
  id: string;
  name: string;
  evaluatedStudentCnt: number;
  studentCnt: number;
}

// 定义 API 响应类型
interface ApiResponse {
  list: ClassItem[];
  total?: number;
  page?: number;
  size?: number;
}

// 统计数据类型定义
interface ClassStats {
  observed: number; // 已观察
  pending: number; // 待观察
  records: number; // 记录数
  avgObservation: number; // 人均观察
  observationRate: number; // 观察率
}

// 计算班级统计数据
const calculateClassStats = (classItem: ClassItem): ClassStats => {
  const observed = classItem.evaluatedStudentCnt;
  const pending = classItem.studentCnt - classItem.evaluatedStudentCnt;
  const records = classItem.evaluatedStudentCnt; // 假设每人一条记录
  const avgObservation = observed > 0 ? 1.1 : 0; // 简化为固定值，实际应根据业务逻辑计算
  const observationRate =
    classItem.studentCnt > 0 ? (observed / classItem.studentCnt) * 100 : 0;

  return {
    observed,
    pending,
    records,
    avgObservation,
    observationRate
  };
};

// 统计展示组件
const ClassStatsDisplay = ({ stats }: { stats: ClassStats }) => {
  return (
    <div className="grid grid-cols-5 gap-2 mt-4 py-3 px-2 bg-gray-50 rounded-lg">
      <div className="text-center">
        <div className="text-lg font-bold text-gray-800 mb-1">
          {stats.observed}
        </div>
        <div className="text-xs text-gray-500 font-medium">已观察</div>
      </div>
      <div className="text-center">
        <div className="text-lg font-bold text-gray-800 mb-1">
          {stats.pending}
        </div>
        <div className="text-xs text-gray-500 font-medium">待观察</div>
      </div>
      <div className="text-center">
        <div className="text-lg font-bold text-gray-800 mb-1">
          {stats.records}
        </div>
        <div className="text-xs text-gray-500 font-medium">记录数</div>
      </div>
      <div className="text-center">
        <div className="text-lg font-bold text-gray-800 mb-1">
          {stats.avgObservation.toFixed(1)}
        </div>
        <div className="text-xs text-gray-500 font-medium">人均观察</div>
      </div>
      <div className="text-center">
        <div className="text-lg font-bold text-gray-800 mb-1">
          {stats.observationRate.toFixed(0)}%
        </div>
        <div className="text-xs text-gray-500 font-medium">观察率</div>
      </div>
    </div>
  );
};

export default function App() {
  if (typeof document !== 'undefined') {
    document.title = '观察记录';
  }
  const router = useRouter();

  // 时间选择相关状态
  const [selectedTimeRange, setSelectedTimeRange] = useState<TimeRange | null>(
    null
  );

  // 使用 react-query 获取班级列表数据
  const { data, isLoading, error } = useQuery<ApiResponse>({
    queryKey: ['observationClassList'],
    queryFn: () =>
      getObservationClassList({}) as unknown as Promise<ApiResponse>
  });

  // 获取班级列表，如果数据不存在则提供空数组作为默认值
  const classes = data?.list || [];

  // 处理时间选择确认
  const handleTimeConfirm = (timeRange: TimeRange) => {
    setSelectedTimeRange(timeRange);
    // 这里可以根据选择的时间进行数据筛选
    console.log('选择的时间段:', timeRange);
  };

  return (
    <main className="bg-slate-50">
      <div className="p-4">
        {/* 时间选择器 */}
        <TimeSelector onConfirm={handleTimeConfirm} className="mb-4" />
        <div className="flex justify-between items-center mb-4 px-2">
          <div
            className="flex flex-col items-center space-y-2 cursor-pointer"
            onClick={() => {
              router.push('/pbl/');
            }}
          >
            <div
              className={
                'w-12 h-12 rounded-full flex items-center justify-center bg-emerald-100'
              }
            >
              <BookOpenCheck className="size-5 text-emerald-500" />
            </div>
            <span className="text-xs text-gray-700">PBL 教学</span>
          </div>
          <div
            className="flex flex-col items-center space-y-2 cursor-pointer"
            onClick={() => {
              router.push('/pbl/record/report');
            }}
          >
            <div
              className={
                'w-12 h-12 rounded-full flex items-center justify-center bg-orange-100'
              }
            >
              <FileText className="w-5 h-5 text-orange-500" />
            </div>
            <span className="text-xs text-gray-700">评价报告</span>
          </div>
          <div
            className="flex flex-col items-center space-y-2 cursor-pointer"
            onClick={() => {
              router.push('/pbl/material?isShowPblTaskMenu=true');
            }}
          >
            <div
              className={
                'w-12 h-12 rounded-full flex items-center justify-center bg-indigo-100'
              }
            >
              <Library className="w-5 h-5 text-indigo-500" />
            </div>
            <span className="text-xs text-gray-700">素材管理</span>
          </div>
          {/* <div
            className="flex flex-col items-center space-y-2 cursor-pointer"
            onClick={() => {
              router.push('/pbl/record/guide');
            }}
          >
            <div
              className={
                'w-12 h-12 rounded-full flex items-center justify-center bg-purple-100'
              }
            >
              <Compass className="w-5 h-5 text-purple-500" />
            </div>
            <span className="text-xs text-gray-700">观察指引</span>
          </div> */}
          <div
            className="flex flex-col items-center space-y-2 cursor-pointer"
            onClick={() => {
              router.push('/pbl/record/settings');
            }}
          >
            <div
              className={
                'w-12 h-12 rounded-full flex items-center justify-center bg-blue-100'
              }
            >
              <Settings className="w-5 h-5 text-blue-500" />
            </div>
            <span className="text-xs text-gray-700">系统设置</span>
          </div>
        </div>
        <div className="">
          {isLoading ? (
            <div className="text-center py-10">加载中...</div>
          ) : error ? (
            <div className="text-center py-10 text-red-500">
              加载失败，请稍后重试
            </div>
          ) : classes.length === 0 ? (
            <div className="text-center py-10 text-gray-500">暂无班级数据</div>
          ) : (
            classes.map((classItem: ClassItem) => {
              const stats = calculateClassStats(classItem);

              return (
                <div
                  key={classItem.id}
                  onClick={() => {
                    router.push(
                      `/pbl/record/class?id=${classItem.id}&name=${classItem.name}&isShowPblTaskMenu=true`
                    );
                  }}
                  className="card-hover mb-4 cursor-pointer rounded-xl bg-white px-4 py-5 shadow-sm"
                >
                  <div className="flex items-start justify-between">
                    <div>
                      <h2 className="text-xl font-semibold flex items-center gap-2">
                        <Users className="w-5 h-5 text-gray-600" />
                        {classItem.name}
                      </h2>
                    </div>
                  </div>
                  {classItem.evaluatedStudentCnt === 0 ? (
                    <div className="mt-3 text-gray-500">还没有记录</div>
                  ) : (
                    <ClassStatsDisplay stats={stats} />
                  )}
                </div>
              );
            })
          )}
        </div>
      </div>
    </main>
  );
}
