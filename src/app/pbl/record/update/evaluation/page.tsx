'use client';
import { Dialog, Toast } from 'antd-mobile';
import { useAtom } from 'jotai';
import { Check, Trash2 } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useRef } from 'react';
import {
  createObservationAbility,
  deleteObservationAbility,
  updateObservationAbility,
} from '@/api/pbl';
import { evaluationAtom, studentAtom } from '@/store/pbl';
import Evaluation, {
  type EvaluationRef,
} from '../../create/components/Evaluation';

const EvaluationPage = () => {
  const searchParams = useSearchParams();
  const isEdit = searchParams?.get('isEdit');
  const [student] = useAtom(studentAtom);
  console.log('🚀 ~ student:', student);
  const router = useRouter();
  const evaluationRefs = useRef<EvaluationRef>(null);
  const [allEvaluations, setAllEvaluations] = useAtom(evaluationAtom);
  console.log('🚀 ~ allEvaluations:', allEvaluations);

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = `${student?.name} 幼儿能力评估`;
    }
  }, []);

  const submit = () => {
    const studentEvaluations = allEvaluations[student?.id] || {};

    const abilities = Object.entries(studentEvaluations)
      .filter(([_, value]) => value === true)
      .map(([abilityId]) => ({ abilityId: Number(abilityId) }));
    if (isEdit) {
      updateObservationAbility(student?.evaluation?.evaluationId, {
        studentId: student?.id,
        observationId: student?.evaluation?.observationId,
        abilities,
      }).then(() => {
        Toast.show({
          content: '保存成功',
          icon: 'success',
        });
        router.back();
      });
    } else {
      createObservationAbility({
        studentId: student?.id,
        deptId: student?.deptId,
        observationId: student?.evaluation?.observationId,
        abilities,
      }).then(() => {
        Toast.show({
          content: '保存成功',
          icon: 'success',
        });
        router.back();
      });
    }
  };

  const deleteStudent = () => {
    Dialog.confirm({
      content: '确定从观察记录中删除此学生吗？',
      cancelText: '取消',
      confirmText: <div className="text-red-400">确认删除</div>,
      onConfirm: async () => {
        deleteObservationAbility(student?.evaluation?.evaluationId).then(() => {
          Toast.show({
            content: '删除成功',
            icon: 'success',
          });
          router.back();
        });
      },
    });
  };

  return (
    <div className="pb-20">
      <div className="p-3">
        <div className="mb-3 flex items-center justify-between">
          <div className="flex items-center">
            <img
              alt=""
              className="mr-2 h-8 w-8 rounded-full"
              src={student?.avatar || ''}
            />
            <span className="font-medium">{student?.name}</span>
          </div>
          {isEdit && (
            <div className="text-red-500 text-xs" onClick={deleteStudent}>
              <Trash2 className="h-4 w-4" />
            </div>
          )}
        </div>
        <Evaluation ref={evaluationRefs} studentId={student?.id} />
      </div>
      <div className="fixed right-0 bottom-0 left-0 flex items-center justify-center pb-3">
        <button
          className="rounded-full bg-indigo-500 px-8 py-3 font-medium text-base text-white shadow-none focus:outline-none focus:ring-4 dark:focus:ring-blue-800"
          onClick={submit}
          type="button"
        >
          <div className="flex items-center justify-center">
            <Check /> <span className="ml-1">保存</span>
          </div>
        </button>
      </div>
    </div>
  );
};

export default EvaluationPage;
