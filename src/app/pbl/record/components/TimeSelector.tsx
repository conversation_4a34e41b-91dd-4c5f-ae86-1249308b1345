'use client';

import { ChevronDown } from 'lucide-react';
import { clsx } from 'clsx';
import { Popup, Tabs } from 'antd-mobile';
import { useState, useMemo, useCallback } from 'react';

// 学期选项类型
export interface SemesterOption {
  id: string;
  name: string;
}

// 月份选项类型
export interface MonthOption {
  id: string;
  name: string;
}

// 时间段类型
export interface TimeRange {
  startTime: string;
  endTime: string;
  semester?: SemesterOption;
  month?: MonthOption;
}

// 组件Props类型
export interface TimeSelectorProps {
  onConfirm: (timeRange: TimeRange) => void;
  defaultSemester?: SemesterOption;
  defaultMonth?: MonthOption;
  semesterOptions?: SemesterOption[];
  monthOptions?: MonthOption[];
  className?: string;
}

// 默认学期数据
const defaultSemesterOptions: SemesterOption[] = [
  { id: '2024-2025-2', name: '2024-2025 下学期' },
  { id: '2024-2025-1', name: '2024-2025 上学期' },
  { id: '2023-2024-2', name: '2023-2024 下学期' },
  { id: '2023-2024-1', name: '2023-2024 上学期' }
];

// 默认月份数据
const defaultMonthOptions: MonthOption[] = [
  { id: '2024-09', name: '2024年9月' },
  { id: '2024-10', name: '2024年10月' },
  { id: '2024-11', name: '2024年11月' },
  { id: '2024-12', name: '2024年12月' },
  { id: '2025-01', name: '2025年1月' },
  { id: '2025-02', name: '2025年2月' },
  { id: '2025-03', name: '2025年3月' },
  { id: '2025-04', name: '2025年4月' },
  { id: '2025-05', name: '2025年5月' },
  { id: '2025-06', name: '2025年6月' }
];

// 计算学期时间段
const calculateSemesterTimeRange = (
  semester: SemesterOption
): { startTime: string; endTime: string } => {
  try {
    const parts = semester.id.split('-');
    if (parts.length !== 3) {
      throw new Error('Invalid semester ID format');
    }

    const year1 = parts[0] || '';
    const year2 = parts[1] || '';
    const term = parts[2] || '';

    const startYear = Number.parseInt(year1, 10);
    const endYear = Number.parseInt(year2, 10);

    // 验证年份有效性
    if (
      Number.isNaN(startYear) ||
      Number.isNaN(endYear) ||
      startYear < 2000 ||
      endYear > 2100
    ) {
      throw new Error('Invalid year in semester ID');
    }

    if (term === '1') {
      // 上学期：9月1日 - 1月31日
      return {
        startTime: `${startYear}-09-01`,
        endTime: `${endYear}-01-31`
      };
    }

    if (term === '2') {
      // 下学期：2月1日 - 6月30日
      return {
        startTime: `${endYear}-02-01`,
        endTime: `${endYear}-06-30`
      };
    }

    throw new Error('Invalid term in semester ID');
  } catch (error) {
    console.warn('Error calculating semester time range:', error);
    // 返回默认值
    const currentYear = new Date().getFullYear();
    return {
      startTime: `${currentYear}-09-01`,
      endTime: `${currentYear + 1}-01-31`
    };
  }
};

// 计算月份时间段
const calculateMonthTimeRange = (
  month: MonthOption
): { startTime: string; endTime: string } => {
  try {
    const parts = month.id.split('-');
    if (parts.length !== 2) {
      throw new Error('Invalid month ID format');
    }

    const year = parts[0] || '';
    const monthNum = parts[1] || '';

    const yearInt = Number.parseInt(year, 10);
    const monthInt = Number.parseInt(monthNum, 10);

    // 验证年份和月份有效性
    if (
      Number.isNaN(yearInt) ||
      Number.isNaN(monthInt) ||
      yearInt < 2000 ||
      yearInt > 2100 ||
      monthInt < 1 ||
      monthInt > 12
    ) {
      throw new Error('Invalid year or month in month ID');
    }

    // 计算该月的最后一天
    const lastDay = new Date(yearInt, monthInt, 0).getDate();

    return {
      startTime: `${year}-${monthNum.padStart(2, '0')}-01`,
      endTime: `${year}-${monthNum.padStart(2, '0')}-${lastDay.toString().padStart(2, '0')}`
    };
  } catch (error) {
    console.warn('Error calculating month time range:', error);
    // 返回默认值（当前月）
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    const lastDay = new Date(year, month, 0).getDate();

    return {
      startTime: `${year}-${month.toString().padStart(2, '0')}-01`,
      endTime: `${year}-${month.toString().padStart(2, '0')}-${lastDay.toString().padStart(2, '0')}`
    };
  }
};

export default function TimeSelector({
  onConfirm,
  defaultSemester,
  defaultMonth,
  semesterOptions = defaultSemesterOptions,
  monthOptions = defaultMonthOptions,
  className = ''
}: TimeSelectorProps) {
  // 状态管理
  const [visible, setVisible] = useState(false);
  const [selectedSemester, setSelectedSemester] = useState<SemesterOption>(
    () =>
      defaultSemester || semesterOptions[0] || { id: '', name: '请选择学期' }
  );
  const [selectedMonth, setSelectedMonth] = useState<MonthOption>(
    () => defaultMonth || monthOptions[0] || { id: '', name: '请选择月份' }
  );
  const [activeTab, setActiveTab] = useState('semester');

  // 处理确认选择 - 使用useCallback优化
  const handleConfirm = useCallback(() => {
    // 验证选择的有效性
    if (
      activeTab === 'semester' &&
      (!selectedSemester?.id || selectedSemester.id === '')
    ) {
      return;
    }
    if (
      activeTab === 'month' &&
      (!selectedMonth?.id || selectedMonth.id === '')
    ) {
      return;
    }

    let timeRange: TimeRange;

    if (activeTab === 'semester') {
      const { startTime, endTime } =
        calculateSemesterTimeRange(selectedSemester);
      timeRange = {
        startTime,
        endTime,
        semester: selectedSemester
      };
    } else {
      const { startTime, endTime } = calculateMonthTimeRange(selectedMonth);
      timeRange = {
        startTime,
        endTime,
        month: selectedMonth
      };
    }

    setVisible(false);
    onConfirm(timeRange);
  }, [activeTab, selectedSemester, selectedMonth, onConfirm]);

  // 获取显示文本 - 使用useMemo优化
  const displayText = useMemo(() => {
    if (activeTab === 'semester') {
      return selectedSemester?.name || '选择学期';
    }

    return selectedMonth?.name || '选择月份';
  }, [activeTab, selectedSemester?.name, selectedMonth?.name]);

  // 处理键盘事件
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (event.key === 'Escape') {
        setVisible(false);
      }
      if (event.key === 'Enter') {
        handleConfirm();
      }
    },
    [handleConfirm]
  );

  return (
    <>
      {/* 触发按钮 */}
      <div
        className={clsx('cursor-pointer', className)}
        onClick={() => setVisible(true)}
      >
        <div className="flex items-center">
          <span className="text-base font-medium mr-2">{displayText}</span>
          <ChevronDown className="w-4 h-4" />
        </div>
      </div>

      {/* 时间选择弹窗 */}
      <Popup
        visible={visible}
        onMaskClick={() => setVisible(false)}
        position="bottom"
        bodyStyle={{
          borderTopLeftRadius: '16px',
          borderTopRightRadius: '16px',
          height: '60vh',
          maxHeight: '500px'
        }}
      >
        <div
          className="flex flex-col h-full"
          onKeyDown={handleKeyDown}
          tabIndex={-1}
        >
          {/* 固定头部 */}
          <div className="flex-shrink-0 p-4 border-b border-gray-100">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold text-gray-800">选择时间</h3>
              <button
                type="button"
                onClick={() => setVisible(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>
          </div>

          {/* 可滚动内容区域 */}
          <div className="flex-1 overflow-hidden">
            <Tabs
              activeKey={activeTab}
              onChange={setActiveTab}
              style={{
                '--active-line-color': '#1677ff',
                '--active-title-color': '#1677ff',
                height: '100%'
              }}
            >
              <Tabs.Tab title="学期" key="semester">
                <div className="h-full overflow-y-auto px-4 py-2">
                  {semesterOptions.map((semester) => (
                    <div
                      key={semester.id}
                      className={clsx(
                        'flex items-center justify-between p-4 mb-2 rounded-lg cursor-pointer transition-colors',
                        selectedSemester?.id === semester.id
                          ? 'bg-blue-50 border border-blue-200'
                          : 'bg-gray-50 hover:bg-gray-100'
                      )}
                      onClick={() => setSelectedSemester(semester)}
                    >
                      <span className="text-gray-800 font-medium">
                        {semester.name}
                      </span>
                      {selectedSemester?.id === semester.id && (
                        <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                          <div className="w-2 h-2 bg-white rounded-full" />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </Tabs.Tab>

              <Tabs.Tab title="月份" key="month">
                <div className="h-full overflow-y-auto px-4 py-2">
                  {monthOptions.map((month) => (
                    <div
                      key={month.id}
                      className={clsx(
                        'flex items-center justify-between p-4 mb-2 rounded-lg cursor-pointer transition-colors',
                        selectedMonth?.id === month.id
                          ? 'bg-blue-50 border border-blue-200'
                          : 'bg-gray-50 hover:bg-gray-100'
                      )}
                      onClick={() => setSelectedMonth(month)}
                    >
                      <span className="text-gray-800 font-medium">
                        {month.name}
                      </span>
                      {selectedMonth?.id === month.id && (
                        <div className="w-5 h-5 bg-indigo-500 rounded-full flex items-center justify-center">
                          <div className="w-2 h-2 bg-white rounded-full" />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </Tabs.Tab>
            </Tabs>
          </div>

          {/* 固定底部按钮 */}
          <div className="flex-shrink-0 p-4 border-t border-gray-100">
            <div className="flex gap-3">
              <button
                type="button"
                onClick={() => setVisible(false)}
                className="flex-1 py-3 px-4 border border-gray-300 rounded-full text-gray-700 font-medium hover:bg-gray-50 transition-colors"
              >
                取消
              </button>
              <button
                type="button"
                onClick={handleConfirm}
                disabled={
                  (activeTab === 'semester' &&
                    (!selectedSemester?.id || selectedSemester.id === '')) ||
                  (activeTab === 'month' &&
                    (!selectedMonth?.id || selectedMonth.id === ''))
                }
                className="flex-1 py-3 px-4 bg-indigo-500 text-white rounded-full font-medium hover:bg-indigo-600 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                确定
              </button>
            </div>
          </div>
        </div>
      </Popup>
    </>
  );
}
