import type { RegionCategory } from './types';

// 模拟区域数据 - 基于 material/create/page.tsx 中的 regionData
export const mockRegionCategories: RegionCategory[] = [
  {
    id: 'indoor',
    title: '室内',
    items: [
      { id: 'indoor_1', label: '活动室', value: 'indoor_1' },
      { id: 'indoor_2', label: '教室', value: 'indoor_2' },
      { id: 'indoor_3', label: '洗漱区', value: 'indoor_3' },
      { id: 'indoor_4', label: '走廊', value: 'indoor_4' },
      { id: 'indoor_5', label: '楼梯', value: 'indoor_5' },
      { id: 'indoor_6', label: '游戏室', value: 'indoor_6' },
      { id: 'indoor_7', label: '666', value: 'indoor_7' },
      { id: 'indoor_8', label: '小山坡', value: 'indoor_8' }
    ]
  },
  {
    id: 'corner',
    title: '区角',
    items: [
      { id: 'corner_1', label: '科学区', value: 'corner_1' },
      { id: 'corner_2', label: '美工区', value: 'corner_2' },
      { id: 'corner_3', label: '阅读区', value: 'corner_3' },
      { id: 'corner_4', label: '建构区', value: 'corner_4' },
      { id: 'corner_5', label: '益智区', value: 'corner_5' },
      { id: 'corner_6', label: '生活区', value: 'corner_6' },
      { id: 'corner_7', label: '语言区', value: 'corner_7' }
    ]
  },
  {
    id: 'outdoor',
    title: '户外',
    items: [
      { id: 'outdoor_1', label: '公共区域', value: 'outdoor_1' },
      { id: 'outdoor_2', label: '操场', value: 'outdoor_2' },
      { id: 'outdoor_3', label: '攀爬区', value: 'outdoor_3' }
    ]
  }
];
