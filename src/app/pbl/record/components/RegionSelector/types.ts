// 区域选择组件的类型定义

// 单个区域选项
export interface RegionOption {
  id: string;
  label: string;
  value: string;
}

// 区域分类
export interface RegionCategory {
  id: string;
  title: string;
  items: RegionOption[];
}

// 选中的区域信息
export interface SelectedRegion {
  categoryId: string;
  categoryTitle: string;
  region: RegionOption;
}

// 组件属性接口
export interface RegionSelectorProps {
  onConfirm: (region: SelectedRegion | null) => void;
  defaultRegion?: SelectedRegion | null;
  className?: string;
  placeholder?: string;
}
