'use client';

import { ChevronDown, X } from 'lucide-react';
import { clsx } from 'clsx';
import { Popup } from 'antd-mobile';
import { useState, useMemo, useCallback } from 'react';

import type {
  RegionSelectorProps,
  SelectedRegion,
  RegionOption,
  RegionCategory
} from './types';
import { mockRegionCategories } from './mockData';

export default function RegionSelector({
  onConfirm,
  defaultRegion = null,
  className = '',
  placeholder = '全部区域'
}: RegionSelectorProps) {
  // 状态管理
  const [visible, setVisible] = useState(false);
  const [selectedRegion, setSelectedRegion] = useState<SelectedRegion | null>(
    defaultRegion
  );
  const [categories] = useState<RegionCategory[]>(mockRegionCategories);

  // 处理区域选择 - 使用useCallback优化
  const handleRegionSelect = useCallback(
    (categoryId: string, categoryTitle: string, region: RegionOption) => {
      const newSelection: SelectedRegion = {
        categoryId,
        categoryTitle,
        region
      };

      // 如果点击的是已选中的区域，则取消选中
      if (selectedRegion?.region.id === region.id) {
        setSelectedRegion(null);
      } else {
        setSelectedRegion(newSelection);
      }
    },
    [selectedRegion]
  );

  // 处理确认选择 - 使用useCallback优化
  const handleConfirm = useCallback(() => {
    setVisible(false);
    onConfirm(selectedRegion);
  }, [selectedRegion, onConfirm]);

  // 处理取消选择
  const handleCancel = useCallback(() => {
    setVisible(false);
    // 恢复到之前的选择状态
    setSelectedRegion(defaultRegion);
  }, [defaultRegion]);

  // 获取显示文本 - 使用useMemo优化
  const displayText = useMemo(() => {
    if (selectedRegion) {
      return selectedRegion.region.label;
    }
    return placeholder;
  }, [selectedRegion, placeholder]);

  // 处理键盘事件
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (event.key === 'Escape') {
        handleCancel();
      }
      if (event.key === 'Enter') {
        handleConfirm();
      }
    },
    [handleConfirm, handleCancel]
  );

  return (
    <>
      {/* 触发按钮 */}
      <div
        className={clsx('cursor-pointer', className)}
        onClick={() => setVisible(true)}
      >
        <div className="flex items-center">
          <span className="text-base font-medium mr-2">{displayText}</span>
          <ChevronDown className="w-4 h-4" />
        </div>
      </div>

      {/* 区域选择弹窗 */}
      <Popup
        visible={visible}
        onMaskClick={handleCancel}
        position="bottom"
        bodyStyle={{
          borderTopLeftRadius: '16px',
          borderTopRightRadius: '16px',
          height: '60vh',
          maxHeight: '500px'
        }}
      >
        <div
          className="flex flex-col h-full"
          onKeyDown={handleKeyDown}
          tabIndex={-1}
        >
          {/* 固定头部 */}
          <div className="flex-shrink-0 p-4 border-b border-gray-100">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold text-gray-800">
                选择观察区域
              </h3>
              <button
                type="button"
                onClick={handleCancel}
                className="p-1 rounded-full hover:bg-gray-100"
              >
                <X className="w-5 h-5 text-gray-500" />
              </button>
            </div>
          </div>

          {/* 可滚动内容区域 */}
          <div className="flex-1 overflow-y-auto p-4">
            {categories.map((category) => (
              <div key={category.id} className="mb-6">
                {/* 分类标题 */}
                <div className="flex items-center mb-3">
                  <div className="w-1 h-4 bg-indigo-500 rounded mr-2" />
                  <h4 className="text-base font-medium text-gray-700">
                    {category.title}
                  </h4>
                </div>

                {/* 区域选项网格 */}
                <div className="grid grid-cols-3 gap-3">
                  {category.items.map((item) => (
                    <button
                      type="button"
                      key={item.id}
                      onClick={() =>
                        handleRegionSelect(category.id, category.title, item)
                      }
                      className={clsx(
                        'px-4 py-3 rounded-lg text-sm font-medium transition-colors',
                        'border border-gray-200 hover:border-gray-300',
                        selectedRegion?.region.id === item.id
                          ? 'bg-indigo-50 border-indigo-500 text-indigo-700'
                          : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                      )}
                    >
                      {item.label}
                    </button>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* 固定底部按钮 */}
          <div className="flex-shrink-0 p-4 border-t border-gray-100">
            <div className="flex gap-3">
              <button
                type="button"
                onClick={handleCancel}
                className="flex-1 py-3 px-4 border border-gray-300 rounded-full text-gray-700 font-medium hover:bg-gray-50 transition-colors"
              >
                取消
              </button>
              <button
                type="button"
                onClick={handleConfirm}
                className="flex-1 py-3 px-4 bg-indigo-500 text-white rounded-full font-medium hover:bg-blue-600 transition-colors"
              >
                确定
              </button>
            </div>
          </div>
        </div>
      </Popup>
    </>
  );
}

// 导出类型，方便外部使用
export type {
  RegionSelectorProps,
  SelectedRegion,
  RegionOption,
  RegionCategory
} from './types';
