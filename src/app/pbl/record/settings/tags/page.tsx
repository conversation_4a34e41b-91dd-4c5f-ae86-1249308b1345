'use client';
import { Button, Dialog, Form, Input, List, Toast, Tag } from 'antd-mobile';
import { Plus, Edit, Trash2, Lock } from 'lucide-react';
import React, { useEffect, useState } from 'react';

interface TagItem {
  id: number;
  name: string;
  type: 'system' | 'custom';
}

const TagManagePage = () => {
  const [tags, setTags] = useState<TagItem[]>([]);
  const [form] = Form.useForm();

  useEffect(() => {
    const fetchTags = async () => {
      // Mock data - 系统标签
      const systemTags: TagItem[] = [
        { id: 1, name: '自由游戏', type: 'system' },
        { id: 2, name: '集体教学', type: 'system' },
        { id: 3, name: '小组合作', type: 'system' },
        { id: 5, name: '角色扮演', type: 'system' },
        { id: 6, name: '建构操作', type: 'system' },
        { id: 7, name: '科学探究', type: 'system' },
        { id: 8, name: '艺术创作', type: 'system' },
        { id: 9, name: '运动挑战', type: 'system' },
        { id: 10, name: '主题探究', type: 'system' }
      ];

      // 自定义标签
      const customTags: TagItem[] = [
        { id: 11, name: '户外活动', type: 'custom' },
        { id: 12, name: '音乐律动', type: 'custom' },
        { id: 13, name: '数学游戏', type: 'custom' },
        { id: 14, name: '语言表达', type: 'custom' },
        { id: 15, name: '社会实践', type: 'custom' }
      ];

      setTags([...systemTags, ...customTags]);
    };
    fetchTags();
  }, []);

  const onAdd = () => {
    form.resetFields();
    Dialog.show({
      title: '添加自定义标签',
      content: (
        <Form form={form} layout="horizontal">
          <Form.Item
            name="name"
            label="名称"
            rules={[{ required: true, message: '请输入标签名称' }]}
          >
            <Input placeholder="请输入标签名称" />
          </Form.Item>
        </Form>
      ),
      closeOnAction: true,
      actions: [
        [
          {
            key: 'cancel',
            text: '取消'
          },
          {
            key: 'submit',
            text: '提交',
            bold: true,
            onClick: async () => {
              try {
                const values = await form.validateFields();
                const newTag: TagItem = {
                  id: Date.now(),
                  name: values.name,
                  type: 'custom'
                };
                setTags([...tags, newTag]);
                Toast.show({
                  icon: 'success',
                  content: '添加成功'
                });
              } catch (error) {
                console.log(error);
              }
            }
          }
        ]
      ]
    });
  };

  const onEdit = (tag: TagItem) => {
    if (tag.type === 'system') {
      Toast.show({
        icon: 'fail',
        content: '系统标签不可编辑'
      });
      return;
    }

    form.resetFields();
    form.setFieldsValue({ name: tag.name });
    Dialog.show({
      title: '编辑自定义标签',
      content: (
        <Form form={form} layout="horizontal">
          <Form.Item
            name="name"
            label="名称"
            rules={[{ required: true, message: '请输入标签名称' }]}
          >
            <Input placeholder="请输入标签名称" />
          </Form.Item>
        </Form>
      ),
      closeOnAction: true,
      actions: [
        [
          {
            key: 'cancel',
            text: '取消'
          },
          {
            key: 'submit',
            text: '提交',
            bold: true,
            onClick: async () => {
              try {
                const values = await form.validateFields();
                const updatedTags = tags.map((t) =>
                  t.id === tag.id ? { ...t, name: values.name } : t
                );
                setTags(updatedTags);
                Toast.show({
                  icon: 'success',
                  content: '更新成功'
                });
              } catch (error) {
                console.log(error);
              }
            }
          }
        ]
      ]
    });
  };

  const onDelete = (tag: TagItem) => {
    if (tag.type === 'system') {
      Toast.show({
        icon: 'fail',
        content: '系统标签不可删除'
      });
      return;
    }

    Dialog.confirm({
      content: `确定要删除标签"${tag.name}"吗？`,
      onConfirm: async () => {
        const updatedTags = tags.filter((t) => t.id !== tag.id);
        setTags(updatedTags);
        Toast.show({
          icon: 'success',
          content: '删除成功'
        });
      }
    });
  };

  // 分组标签
  const systemTags = tags.filter((tag) => tag.type === 'system');
  const customTags = tags.filter((tag) => tag.type === 'custom');

  return (
    <div className=" bg-slate-50">
      <div className="flex justify-between items-center p-4">
        <h1 className="text-lg font-medium">标签管理</h1>
        <Button size="small" color="primary" onClick={onAdd}>
          添加标签
        </Button>
      </div>

      {/* 自定义标签 */}
      <List mode="card" header="自定义标签">
        {customTags.map((tag) => (
          <List.Item
            key={tag.id}
            extra={
              <div className="flex gap-2">
                <Button
                  size="small"
                  color="primary"
                  fill="none"
                  onClick={() => onEdit(tag)}
                >
                  <Edit className="w-4 h-4" />
                </Button>
                <Button
                  size="small"
                  color="danger"
                  fill="none"
                  onClick={() => onDelete(tag)}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            }
          >
            <div className="flex items-center gap-2">
              <span>{tag.name}</span>
            </div>
          </List.Item>
        ))}
      </List>
      {/* 系统标签 */}

      <List mode="card" header="系统标签" className="mt-4">
        {systemTags.map((tag) => (
          <List.Item key={tag.id}>
            <div className="flex items-center gap-2">
              <span>{tag.name}</span>
            </div>
          </List.Item>
        ))}
      </List>
    </div>
  );
};

export default TagManagePage;
