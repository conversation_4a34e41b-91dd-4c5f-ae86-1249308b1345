'use client';

import { <PERSON><PERSON>, SpinLoading, Toast } from 'antd-mobile';
import FormRender, { type SchemaBase, useForm } from 'form-render-mobile';
import Cookies from 'js-cookie';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { getFormDetail, getSubmitDetail, submit, update } from '@/api/form';
import api from '@/lib/api';
import { postMessage } from '@/utils';
// import { schema as testSchema } from '../create/components/data';
import address from '../../workflow/create/components/widgets/Address';
import attachment from '../../workflow/create/components/widgets/Attachment';
import checkbox from '../../workflow/create/components/widgets/Checkbox';
// import checkboxes from '../../workflow/create/components/widgets/Checkboxes';
import image from '../../workflow/create/components/widgets/Image';
import richText from '../../workflow/create/components/widgets/RichText';
import signature from '../../workflow/create/components/widgets/Signature';
import Table from '../../workflow/create/components/widgets/Table';
import video from '../../workflow/create/components/widgets/Video';

const findLabels = (value: any[], options: any[]) => {
  if (!(isValidateArray(value) && isValidateArray(options))) {
    return [];
  }

  return value.map((v) => options.find((o) => o.value === v)?.label);
};

export function getFormat(format: string) {
  switch (format) {
    case 'date':
      return 'YYYY-MM-dd';
    case 'year':
      return 'YYYY';
    case 'month':
      return 'YYYY-MM';
    case 'week':
      return 'YYYY-w';
    case 'hour':
      return 'YYYY-MM-dd hh';
    case 'minute':
      return 'YYYY-MM-dd hh:mm';
    case 'second':
      return 'YYYY-MM-dd hh:mm:ss';
    case 'week-day':
      return 'w-d';
    default:
      return 'YYYY-MM-dd';
  }
}

// const flatCascaderOptions = (options: any[]) => {
//   const result = [];

//   const walk = (list: any[]) => {
//     list.forEach((i) => {
//       result.push(i);
//       if (isValidateArray(i.children)) {
//         walk(i.children);
//       }
//     });
//   };

//   walk(options);
//   return result;
// };

const isValidateArray = (list: unknown) =>
  Array.isArray(list) && list.length > 0;

const getLabel = (value: any, schema: any) => {
  const { props } = schema;
  console.log('🚀 ~ props:', props);

  let __html = '';

  switch (schema.widget) {
    case 'input':
    case 'textArea':
    case 'inputNumber':
    case 'amountNumber':
    case 'phoneNumber':
    case 'richText':
    case 'rate':
    case 'stepper':
    case 'datePicker':
      __html = value;
      break;
    case 'image':
      __html = value?.length ? `${value.length}张图片` : '-';
      break;
    case 'video':
      __html = value?.length ? `${value.length}个视频` : '-';
      break;
    case 'attachment':
      __html = value?.length ? `${value.length}个附件` : '-';
      break;
    case 'signature':
      __html = value ? '已签名' : '-';
      break;
    case 'slider':
      if (isValidateArray(value)) {
        __html = value.join(' - ');
      } else {
        __html = value;
      }
      break;
    case 'checkbox':
    case 'selector':
      {
        const { options } = props;
        if (isValidateArray(value)) {
          __html = findLabels(value, options).join('，');
        }
      }
      break;
    case 'switch': {
      const { uncheckedText = '否', checkedText = '是' } = props || {};
      __html = value ? checkedText : uncheckedText;
      break;
    }
    case 'address':
      if (isValidateArray(value)) {
        __html = value.map((item) => item.label).join(' - ');
      }
      break;
    case 'radio':
      {
        const { options } = props;
        __html = options.find((o) => o.value === value)?.label;
      }
      break;
    case 'picker': {
      const { options } = props;
      if (options?.length) {
        __html = findLabels(value, options).join('-') || '-';
      }
      break;
    }
    case 'group': {
      // 地址控件
      const { area, detail } = props || {};
      if (area && detail) {
        const areaArr = value.area.map((item) => item.label).join('-');
        __html = `${areaArr} ${value.detail || '-'}`;
      }
      break;
    }
    default:
      __html = '-';
  }
  return __html;
};

const testData = {
  type: 'object',
  displayType: 'column',
  properties: {
    'form-qZ5w2FLx7Sdm': {
      title: '检查部门',
      description: '',
      type: 'array',
      placeholder: '请选择',
      widget: 'picker',
      props: {
        options: [
          {
            label: '行政部',
            value: '1',
          },
          {
            label: '财务部',
            value: '2',
          },
          {
            label: '教学部',
            value: '3',
          },
          {
            label: '后勤部',
            value: '4',
          },
        ],
      },
      required: false,
      printable: true,
      id: 'form-qZ5w2FLx7Sdm',
      order: 0,
    },
    switch: {
      title: '开关',
      description: '',
      type: 'bool',
      widget: 'switch',
      placeholder: '请选择',
      printable: true,
      exportable: true,
      required: true,
    },
    'form-qZ5w2FLx7Sef': {
      title: '检查部门',
      description: '',
      type: 'array',
      placeholder: '请选择',
      widget: 'table',
      props: {
        columns: [
          {
            key: 'name',
            title: '姓名',
            dataIndex: 'name',
          },
          {
            key: 'age',
            title: '班级',
            dataIndex: 'age',
          },
          {
            key: 'email',
            title: '333',
            dataIndex: 'email',
          },
        ],
      },
      required: false,
      printable: true,
      id: 'form-qZ5w2FLx7Sef',
      order: 0,
    },
    group1: {
      type: 'object',
      widget: 'group',
      title: '检查项目明细',
      order: 1,
      properties: {
        'form-HB3lF6BMO775f': {
          title: '每日一次消毒',
          type: 'array',
          widget: 'selector',
          props: {
            multiple: true,
            options: [
              { label: '鞋柜', value: '1' },
              { label: '窗', value: '2' },
              { label: '门', value: '3' },
              { label: '书包柜', value: '4' },
              { label: '口杯', value: '5' },
              { label: '被褥', value: '6' },
              { label: '环创装饰物', value: '7' },
              { label: '储物架', value: '8' },
              { label: '教师办公设施', value: '9' },
            ],
          },
          required: false,
          printable: true,
          id: 'form-HB3lF6BMO775f',
          order: 1,
        },
        'form-HB3lF6BMO7754': {
          title: '每日两次消毒',
          type: 'array',
          widget: 'selector',
          props: {
            multiple: true,
            options: [
              { label: '椅子', value: '1' },
              { label: '玩具', value: '2' },
              { label: '榻榻米', value: '3' },
              { label: '毛巾', value: '4' },
            ],
          },
          required: false,
          printable: true,
          id: 'form-HB3lF6BMO775f',
          order: 1,
        },
        'form-HB3lF6BMO7755': {
          title: '每日三次消毒',
          type: 'array',
          widget: 'selector',
          props: {
            multiple: true,
            options: [
              { label: '门把手', value: '1' },
              { label: '灯具开关', value: '2' },
              { label: '地面', value: '3' },
              { label: '桌子', value: '4' },
              { label: '餐具', value: '5' },
              { label: '洗手间', value: '6' },
              { label: '水龙头', value: '7' },
              { label: '垃圾桶', value: '8' },
            ],
          },
          required: false,
          printable: true,
          id: 'form-HB3lF6BMO775f',
          order: 1,
        },
        'form-HB3lF6BMO7756': {
          title: '每周消毒',
          type: 'array',
          widget: 'selector',
          props: {
            multiple: true,
            options: [
              { label: '墙面', value: '1' },
              { label: '风扇', value: '2' },
            ],
          },
          required: false,
          printable: true,
          id: 'form-HB3lF6BMO775f',
          order: 1,
        },
      },
    },
    group2: {
      type: 'object',
      widget: 'group',
      title: '检查项目明细',
      order: 1,
      layout: 'horizontal',
      columns: 2,
      properties: {
        input: {
          title: '输入框',
          type: 'string',
          widget: 'input',
          column: 3,
          layout: 'row',
          printable: true,
        },
        switch: {
          title: '是否检查',
          type: 'bool',
          widget: 'switch',
          layout: 'row',
        },
        radio: {
          title: '单选',
          type: 'string',
          widget: 'radio',
          layout: 'row',
          printable: true,
          props: {
            options: [
              { label: '未检', value: 'a' },
              { label: '已检', value: 'b' },
              { label: '优秀', value: 'c' },
            ],
          },
        },
      },
    },
    'form-6zUMHAqUXWQT': {
      id: 'form-6zUMHAqUXWQT',
      title: '地址',
      description: '',
      type: 'object',
      widget: 'group',
      layout: 'column',
      printable: true,
      properties: {
        area: {
          title: '地区',
          type: 'array',
          widget: 'address',
          readOnlyWidget: 'address',
          placeholder: '请选择',
        },
        detail: {
          title: '详细地址',
          type: 'string',
          layout: 'column',
          widget: 'textArea',
          placeholder: '请输入详细地址',
          props: {
            rows: 2,
            maxLength: 100,
          },
        },
      },
      order: 1,
    },
    'form-HB3lF6BMO77f': {
      title: '检查日期',
      description: '',
      type: 'string',
      placeholder: '请选择',
      widget: 'datePicker',
      required: false,
      printable: true,
      id: 'form-HB3lF6BMO77f',
      order: 1,
    },
    'form-l1cGrydGHcr0': {
      title: '检查说明',
      description: '',
      type: 'string',
      placeholder: '请输入',
      widget: 'textArea',
      layout: 'column',
      required: false,
      printable: true,
      id: 'form-l1cGrydGHcr0',
      order: 2,
    },
    'form-B1teMmXgKQlT': {
      title: '上级部门',
      description: '',
      type: 'string',
      placeholder: '请输入',
      widget: 'input',
      layout: 'column',
      required: false,
      printable: true,
      id: 'form-B1teMmXgKQlT',
      order: 3,
    },
    'form-FacGX4e7cwLs': {
      title: '接待人',
      description: '',
      type: 'string',
      placeholder: '请输入',
      widget: 'textArea',
      layout: 'column',
      required: false,
      printable: true,
      id: 'form-FacGX4e7cwLs',
      order: 4,
    },
    'form-vVK4zsNdiu9J': {
      title: '检查照片',
      description: '',
      type: 'array',
      placeholder: '请上传图片',
      widget: 'image',
      readOnlyWidget: 'image',
      layout: 'column',
      required: false,
      printable: true,
      id: 'form-vVK4zsNdiu9J',
      order: 5,
    },
    'form-mTE1pkHNsFSH': {
      title: '整改后图片',
      description: '',
      type: 'array',
      placeholder: '请上传图片',
      widget: 'image',
      readOnlyWidget: 'image',
      layout: 'column',
      required: false,
      printable: true,
      id: 'form-mTE1pkHNsFSH',
      order: 6,
    },
    'form-X2bGSDAEL1Eg': {
      id: 'form-X2bGSDAEL1Eg',
      title: '日期时间',
      description: '',
      type: 'string',
      widget: 'datePicker',
      placeholder: '请选择',
      printable: true,
      required: false,
      props: {
        format: 'YYYY-MM-DD HH:mm',
        precision: 'minute',
      },
      order: 7,
    },
    'form-WTHwi1OcppmL': {
      title: '多选',
      description: '',
      type: 'array',
      placeholder: '请选择',
      widget: 'checkbox',
      readOnlyWidget: 'checkbox',
      props: {
        options: [
          {
            label: '的方法发',
            value: '1',
          },
          {
            label: '冲刺冲刺',
            value: '2',
          },
          {
            label: '罚款罚款',
            value: '3',
          },
          {
            label: '飞飞飞',
            value: '4',
          },
          {
            label: '选项5',
            value: '5',
          },
        ],
      },
      required: false,
      printable: true,
      id: 'form-WTHwi1OcppmL',
      order: 8,
    },
  },
};

export default function Index() {
  const searchParams = useSearchParams();
  const form = useForm();
  const instanceId = searchParams?.get('instanceId');
  const submitId = searchParams?.get('submitId');
  const authorization = searchParams?.get('authorization');
  if (authorization) {
    Cookies.set('Authorization', authorization);
  }

  const [schema, setSchema] = useState<SchemaBase | null>(null);
  const [formData, setFormData] = useState<Record<string, unknown> | null>(
    null
  );
  const [dataLoading, setDataLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  const linkageRules = [
    {
      source: 'form-HB3lF6BMO77f',
      target: [
        { type: 'value', name: 'form-l1cGrydGHcr0', responseKey: 'headCount' },
        { type: 'value', name: 'form-B1teMmXgKQlT', responseKey: 'miss' },
      ],
      request: {
        url: 'https://test-ucapp-api.ancda.com/v1/affairs/attendance/statistic/allStudentStat?isDay=1',
        method: 'GET',
        bodyKey: 'date',
      },
    },
    {
      source: 'form-X2bGSDAEL1Eg',
      target: [
        { type: 'schema', name: 'form-WTHwi1OcppmL', responseKey: 'children' },
      ],
      request: {
        url: 'https://test-ucapp-api.ancda.com/v1/contact/school/departments',
        method: 'GET',
        bodyKey: 'date',
      },
    },
  ];

  const watch: Record<string, (val: string | null) => void> = {};
  for (const rule of linkageRules) {
    watch[rule.source] = (val: string | null) => {
      if (val == null || val === '') {
        for (const key of Object.keys(rule.target)) {
          form.setValueByPath(key, undefined);
        }
        return;
      }
      const body: Record<string, string> = {};
      body[rule.request.bodyKey] = val;
      api({
        url: rule.request.url,
        method: rule.request.method,
        params: body,
        headers: { 'Content-Type': 'application/json' },
      }).then((res) => {
        const data = res.data ?? res;
        for (const t of rule.target) {
          if (t.type === 'value') {
            form.setValueByPath(t.name, String(data[t.responseKey]));
          }
          if (t.type === 'schema') {
            form.setSchemaByPath(t.name, {
              props: {
                options: data[t.responseKey].map((item) => ({
                  label: item.name,
                  value: item.id,
                })),
              },
            });
          }
        }
      });
    };
  }

  useEffect(() => {
    if (!(instanceId || submitId)) {
      return;
    }
    if (submitId) {
      setDataLoading(true);
      getSubmitDetail(submitId)
        .then((res: any) => {
          setDataLoading(false);
          if (typeof res === 'object') {
            setSchema(JSON.parse(res.form));
            setFormData(JSON.parse(res.content));
          }
        })
        .catch(() => {
          setDataLoading(false);
        });
    } else if (instanceId) {
      setDataLoading(true);
      getFormDetail(instanceId)
        .then((res: any) => {
          setDataLoading(false);
          if (res.form) {
            setSchema(testData);
          }
        })
        .catch(() => {
          setDataLoading(false);
        });
    }
  }, [instanceId, submitId]);

  const onFinish = (formData: Record<string, unknown>) => {
    if (!schema?.properties) {
      return;
    }
    console.log('🚀 ~ formData:', formData);
    console.log('🚀 ~ schema:', schema);

    const formDataCopy = { ...formData };
    const { properties } = schema;
    for (const key in formDataCopy) {
      if (Object.hasOwn(formDataCopy, key) && properties[key]) {
        formDataCopy[key] = getLabel(formDataCopy[key], properties[key]);
      }
    }
    const data = {
      instanceId,
      form: JSON.stringify(schema),
      content: JSON.stringify(formData),
      result: formDataCopy,
    };
    setSubmitLoading(true);
    // biome-ignore lint/suspicious/noDebugger: <explanation>
    debugger;
    if (submitId) {
      update(submitId, data)
        .then((res: any) => {
          setSubmitLoading(false);
          Toast.show('更新成功');
          postMessage({ goBack: 2, instanceId: res.instanceId });
        })
        .catch(() => {
          setSubmitLoading(false);
        });
    } else {
      submit(data)
        .then((res: any) => {
          setSubmitLoading(false);
          Toast.show('提交成功');
          postMessage({ goBack: 2, instanceId: res.instanceId });
        })
        .catch(() => {
          setSubmitLoading(false);
        });
    }
  };

  const onMount = () => {
    // setLoading(false);
    setIsMounted(true);
    if (submitId && formData) {
      form.setValues(formData);
    }
  };

  return (
    <div className="relative flex h-screen flex-col bg-white ">
      {dataLoading && (
        <div className="flex h-screen w-full items-center justify-center">
          <SpinLoading />
        </div>
      )}
      <div className="">
        {!!schema && (
          <FormRender
            form={form}
            // requiredMarkStyle="text-required"
            // displayType="column"
            mode="card"
            onFinish={onFinish}
            onMount={onMount}
            schema={schema}
            watch={watch}
            widgets={{
              checkbox,
              richText,
              signature,
              image,
              video,
              attachment,
              address,
              Table,
            }}
            // className="-m-2"
          />
        )}
      </div>
      {isMounted && (
        <div className="w-full p-4">
          <Button
            block
            color="primary"
            loading={submitLoading}
            onClick={() => {
              form.submit();
            }}
            size="large"
            type="submit"
          >
            {submitId ? '更新' : '提交'}
          </Button>
        </div>
      )}
    </div>
  );
}
