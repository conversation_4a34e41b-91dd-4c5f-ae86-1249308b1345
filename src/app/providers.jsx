'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Cookies from 'js-cookie';
import Script from 'next/script';
import { NuqsAdapter } from 'nuqs/adapters/next/app';
import React, { useEffect } from 'react';
import { useCommonStore } from '@/store/useCommonStore';

function setThemeByDomain() {
  const { hostname } = window.location;

  if (hostname.includes('baby-mobile')) {
    document.documentElement.style.setProperty(
      '--adm-color-primary',
      '#17C5A6'
    );
  } else {
    document.documentElement.style.setProperty(
      '--adm-color-primary',
      '#4E78FF'
    );
  }
}

export default function Providers({ children }) {
  const [queryClient] = React.useState(() => new QueryClient());
  const setAuthorization = useCommonStore((state) => state.setAuthorization);
  const setVersion = useCommonStore((state) => state.setVersion);
  const setBrand = useCommonStore((state) => state.setBrand);
  const setAppType = useCommonStore((state) => state.setAppType);
  useEffect(() => {
    const authorization = Cookies.get('Authorization');
    const version = Cookies.get('Version');
    const Brand = Cookies.get('Brand');
    const AppType = Cookies.get('App-Type');
    setAuthorization(authorization);
    setVersion(version);
    setBrand(Brand);
    setAppType(AppType);
    setThemeByDomain();
  }, []);

  return (
    <NuqsAdapter>
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    </NuqsAdapter>
  );
}
