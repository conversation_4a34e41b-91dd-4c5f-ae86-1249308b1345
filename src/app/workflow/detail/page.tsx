'use client';

import { ImageViewer } from 'antd-mobile';
import clsx from 'clsx';
import { format } from 'date-fns';
import FormRender, { useForm } from 'form-render-mobile';
import Cookies from 'js-cookie';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { useImmer } from 'use-immer';

import { submitDetail } from '@/api/approval';

import { modeType } from '../create/components/data';
import address from '../create/components/widgets/Address';
import attachment from '../create/components/widgets/Attachment';
import { FileIcon } from '../create/components/widgets/Attachment/ImageConfig';
import checkbox from '../create/components/widgets/Checkbox';
import checkboxes from '../create/components/widgets/Checkboxes';
import image from '../create/components/widgets/Image';
import richText from '../create/components/widgets/RichText';
import signature from '../create/components/widgets/Signature';
import studentPicker from '../create/components/widgets/StudentPicker';
import teacherPicker from '../create/components/widgets/TeacherPicker';
import video from '../create/components/widgets/Video';
import User from './components/User';

export const dynamic = 'force-dynamic';

type State = {
  instanceId: string;
  modelName: string;
  startUserId: string;
  startTime: number;
  form: object;
  formData: object;
  formPerm: any;
  operationPerm: any;
  nodes: any[];
  status: number;
  finishTime: number;
  settings: {
    allowCancel: boolean;
    sign: boolean;
  };
};

function Page() {
  const searchParams = useSearchParams();
  const instanceId = searchParams?.get('instanceId');
  const taskId = searchParams?.get('taskId');
  const authorization = searchParams?.get('authorization');
  if (authorization) {
    Cookies.set('Authorization', authorization);
  }
  const form = useForm();

  const [data, setData] = useState<State>({
    instanceId: '',
    modelName: '',
    startUserId: '',
    startTime: 0,
    form: {},
    formData: {},
    formPerm: {},
    operationPerm: {},
    nodes: [],
    status: 0, // 1：审批中，2：通过，3：拒绝，4：撤回
    finishTime: 0,
    settings: {
      allowCancel: true,
      sign: false,
    },
  });

  const [nodes, setNodes] = useImmer<any[]>([]);

  useEffect(() => {
    if (instanceId) {
      submitDetail(instanceId, { taskId }).then((res: any) => {
        if (res) {
          setData(res);
          if (Array.isArray(res.nodes)) {
            // 同一个节点用户分组
            const group: any = [];
            res.nodes.forEach((item, index) => {
              const findIndex = group.findIndex(
                (i) => i.nodeId === item.nodeId
              );
              if (findIndex === -1 || item.result === 'cancel') {
                group.push({ ...item, users: [item] });
              } else if (group[findIndex].users) {
                group[findIndex].users.push(item);
              }
            });
            setNodes(group);
          }
        }
      });
    }
  }, [instanceId]);

  const onMount = () => {
    if (data.formData) {
      form.setValues(data.formData);
    }
    // setLoading(false);
  };

  const download = (url: string) => {
    window.open(url);
  };

  // 判断节点是否审核通过状态
  const currentNode = (node) => {
    if (data.status !== 1) {
      // 非审批中状态全部通过
      return true;
    }
    if (
      node.nodeType === 'ROOT' ||
      node.nodeType === 'CC' ||
      node.mode === 'OR'
    ) {
      // 一人通过就通过
      if (
        Array.isArray(node.users) &&
        node.users.some((item) => item.result === 'pass')
      ) {
        return true;
      }
    } else if (node.mode === 'AND' || node.mode === 'NEXT') {
      // 需全部人同意
      if (
        Array.isArray(node.users) &&
        node.users.every((item) => item.result === 'pass')
      ) {
        return true;
      }
    }

    if (node.nodeId === 'endEvent' && node.result === 'pass') {
      return true;
    }
    return false;
  };

  return (
    <div className="bg-[#F7F9FF]">
      <div className="flex items-center justify-between bg-white p-4 ">
        <User userId={data.startUserId} />
        {data.status === 1 && (
          <div className="rounded-sm bg-yellow-50 px-2 py-1 text-yellow-500">
            待审核
          </div>
        )}
        {data.status === 2 && (
          <div className="rounded-sm bg-green-50 px-2 py-1 text-green-500">
            已通过
          </div>
        )}
        {data.status === 3 && (
          <div className="rounded-sm bg-red-50 px-2 py-1 text-red-500">
            已拒绝
          </div>
        )}
        {data.status === 4 && (
          <div className="rounded-sm bg-red-50 px-2 py-1 text-red-500">
            已撤回
          </div>
        )}
      </div>
      <div className="relative p-4">
        <div className="rounded bg-white p-2">
          <div className="px-4 pt-2 font-bold text-base">审批详情</div>
          {data.form && Object.keys(data.form).length > 0 && (
            <FormRender
              form={form}
              // displayType="column"
              onFinish={() => {
                console.log('onFinish');
              }}
              onMount={onMount}
              readOnly
              schema={data.form}
              style={{
                '--border-bottom': 'none',
                '--border-inner': 'none',
                '--border-top': 'none',
              }}
              widgets={{
                checkbox,
                checkboxes,
                richText,
                signature,
                image,
                video,
                attachment,
                address,
                studentPicker,
                teacherPicker,
              }}
            />
          )}
        </div>
        {data.status === 2 && (
          <Image
            alt=""
            className="absolute top-6 right-6 z-10 size-14 object-cover"
            height="0"
            sizes="80px"
            src="/images/workflow/pass.png"
            width="0"
          />
        )}
        {data.status === 3 && (
          <Image
            alt=""
            className="absolute top-6 right-6 z-10 size-14 object-cover"
            height="0"
            sizes="80px"
            src="/images/workflow/reject.png"
            width="0"
          />
        )}
        <div className="mt-4 rounded bg-white p-4 text-base">
          {Array.isArray(nodes) && nodes.length > 0 && (
            <ul>
              {nodes.map((item, index) => (
                <li
                  className="relative flex items-baseline gap-8 pb-5"
                  key={`node${index}`}
                >
                  <div
                    className={clsx(
                      'before:absolute before:left-[11px] before:h-full before:bg-slate-200',
                      index === nodes.length - 1
                        ? 'before:w-0'
                        : 'before:w-[2px]'
                    )}
                  >
                    <div
                      className={clsx(
                        'absolute top-1 z-10 flex size-[24px] items-center justify-center overflow-hidden rounded-full border-4 bg-white',
                        currentNode(item)
                          ? 'border-green-500'
                          : 'border-stone-300'
                      )}
                    />
                  </div>
                  <div className="w-full">
                    <div className="mb-1 font-bold text-base leading-none">
                      {item.name}
                    </div>
                    <div className="text-gray-500 text-xs">
                      {item.nodeType !== 'CC' ? modeType[item.mode] || '' : ''}
                    </div>
                    {Array.isArray(item.users) ? (
                      item.users.map((userItem: any, idx: number) => (
                        <div key={`userItem${idx}`}>
                          <div className="my-2 flex w-full items-center justify-between">
                            <div className="flex items-center">
                              <User userId={userItem.userId} withBackground />
                              {userItem.result === 'pass' && (
                                <span className="ml-2 text-green-500 text-sm">
                                  {item.nodeType === 'ROOT' && '已提交'}
                                  {item.nodeType === 'TASK' && '已办理'}
                                  {item.nodeType === 'APPROVAL' && '已同意'}
                                </span>
                              )}
                              {userItem.result === 'cancel' && (
                                <span className="ml-2 text-red-500 text-sm">
                                  {item.nodeType === 'ROOT' && '已撤回'}
                                </span>
                              )}
                              {userItem.result === 'refuse' && (
                                <span className="ml-2 text-red-500">
                                  已拒绝
                                </span>
                              )}
                            </div>
                            <div className="text-sm text-stone-400">
                              {['ROOT', 'TASK', 'APPROVAL'].includes(
                                item.nodeType
                              ) &&
                              (userItem.result === 'pass' ||
                                userItem.result === 'cancel' ||
                                userItem.result === 'reject') &&
                              userItem.finishTime &&
                              userItem.nodeId !== 'endEvent'
                                ? format(
                                    new Date(userItem.finishTime * 1000),
                                    'yyyy-MM-dd HH:mm'
                                  )
                                : null}
                            </div>
                          </div>
                          {Array.isArray(userItem.comments) && (
                            <div className="mb-2 w-full">
                              {userItem.comments.map(
                                (comment: any, index: number) => (
                                  <span
                                    className="text-sm text-stone-500"
                                    key={`comment${index}`}
                                  >
                                    {comment.text}
                                  </span>
                                )
                              )}
                            </div>
                          )}
                          {Array.isArray(userItem.attachments) && (
                            <>
                              <div className="flex w-full flex-wrap">
                                {userItem.attachments
                                  .filter((item: any) => item.type === 'image')
                                  .map((image: any, index: number) => (
                                    <Image
                                      alt=""
                                      className="mr-2 mb-2 size-[136px] rounded object-cover"
                                      height="0"
                                      key={`image${index}`}
                                      onClick={() => {
                                        ImageViewer.show({
                                          image: image.url,
                                        });
                                      }}
                                      sizes="120px"
                                      src={image.url || ''}
                                      width="0"
                                    />
                                  ))}
                              </div>
                              <div className="mt-2 divide-y divide-gray-100 rounded-md">
                                {userItem.attachments
                                  .filter((item: any) => item.type !== 'image')
                                  .map((attachment: any, index: any) => (
                                    <div
                                      className="mt-2 flex items-center "
                                      key={index}
                                    >
                                      <div
                                        className="flex flex-1 items-center"
                                        onClick={() => download(attachment.url)}
                                      >
                                        <FileIcon fileType={attachment.type} />
                                        <div className="ml-1 flex flex-1 flex-col">
                                          <div className="w-full overflow-hidden text-ellipsis break-all text-base">
                                            {attachment.name}
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                              </div>
                            </>
                          )}
                          {userItem.signature ? (
                            <Image
                              alt=""
                              className="w-full object-cover"
                              height="0"
                              sizes="100%"
                              src={userItem.signature}
                              width="0"
                            />
                          ) : null}
                        </div>
                      ))
                    ) : item.userId ? (
                      <div className="mt-2 flex flex-wrap">
                        <User userId={item.userId} withBackground />
                      </div>
                    ) : null}
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
        {/* <ul>
          <li className="relative flex items-baseline gap-12 pb-5">
            <div className="before:absolute before:left-[30px] before:h-full before:w-[4px] before:bg-neutral-200">
              <div className="flex justify-center items-center rounded-full bg-white w-[64px] h-[64px] border-4 border-white absolute top-0 overflow-hidden">
                <Image
                  src="https://unicorn-media.ancda.com/test/test/2023-06-25/6.jpg"
                  alt=""
                  width="0"
                  height="0"
                  sizes="64px"
                  className="object-cover w-[64px] h-[64px]"
                />
              </div>
            </div>
            <div>
              <div className="text-base">刘老师</div>
              <div className="text-sm text-gray-400">发起申请</div>
              <div className="mt-2 text-gray-600 text-sm">
                <div className="w-full flex flex-wrap">
                  {Array.from({ length: 9 }).map((item, key) => {
                    return (
                      <Image
                        key={key}
                        src="https://unicorn-media.ancda.com/test/test/2023-06-25/6.jpg"
                        alt=""
                        width="0"
                        height="0"
                        sizes="120px"
                        className="object-cover w-[136px] h-[136px] mr-2 mb-2 rounded "
                      />
                    )
                  })}
                </div>
                <ul
                  role="list"
                  className="divide-y divide-gray-100 rounded-md border border-gray-200"
                >
                  <li className="flex items-center justify-between py-4 px-3 text-sm leading-6">
                    <span className="truncate font-medium">
                      resume_back_end_developer.pdf
                    </span>
                  </li>
                  <li className="flex items-center justify-between py-4 px-3 text-sm leading-6">
                    <span className="truncate font-medium">
                      resume_back_end_developer.pdf
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </li>
          <li className="relative flex items-baseline gap-12 pb-5">
            <div className="before:absolute before:left-[28px] before:h-full before:w-[4px] before:bg-neutral-200">
              <div className="flex justify-center items-center rounded-full bg-white w-[60px] h-[60px] border-4 border-white absolute top-0 overflow-hidden">
                <Image
                  src="https://unicorn-media.ancda.com/test/test/2023-06-25/6.jpg"
                  alt=""
                  width="0"
                  height="0"
                  sizes="60px"
                  className="object-cover w-[60px] h-[60px]"
                />
              </div>
            </div>
            <div>
              <div className="text-base">刘老师</div>
              <div className="text-sm text-gray-400">发起申请</div>
              <div className="mt-2 text-gray-600 text-sm">
                <div className="w-full flex flex-wrap">
                  {Array.from({ length: 9 }).map((item, key) => {
                    return (
                      <Image
                        key={key}
                        src="https://unicorn-media.ancda.com/test/test/2023-06-25/6.jpg"
                        alt=""
                        width="0"
                        height="0"
                        sizes="120px"
                        className="object-cover w-[136px] h-[136px] mr-2 mb-2 rounded"
                      />
                    )
                  })}
                </div>
                <ul
                  role="list"
                  className="divide-y divide-gray-100 rounded-md border border-gray-200"
                >
                  <li className="flex items-center justify-between py-4 px-3 text-sm leading-6">
                    <span className="truncate font-medium">
                      resume_back_end_developer.pdf
                    </span>
                  </li>
                  <li className="flex items-center justify-between py-4 px-3 text-sm leading-6">
                    <span className="truncate font-medium">
                      resume_back_end_developer.pdf
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </li>
          <li className="relative flex items-baseline gap-12 pb-5">
            <div className="">
              <div className="flex justify-center items-center rounded-full bg-white w-[60px] h-[60px] absolute top-0 overflow-hidden">
                <Image
                  src="https://unicorn-media.ancda.com/production/app/avatar/default_avator.png"
                  alt=""
                  width="0"
                  height="0"
                  sizes="60px"
                  className="object-cover w-[60px] h-[60px]"
                />
              </div>
            </div>
            <div>
              <div className="text-base">刘老师</div>
              <div className="text-sm text-gray-400">发起申请</div>
              <div className="mt-2 text-gray-600 text-sm">
                <div className="w-full flex flex-wrap">
                  {Array.from({ length: 9 }).map((item, key) => {
                    return (
                      <Image
                        key={key}
                        src="https://unicorn-media.ancda.com/test/test/2023-06-25/6.jpg"
                        alt=""
                        width="0"
                        height="0"
                        sizes="120px"
                        className="object-cover w-[136px] h-[136px] mr-2 mb-2 rounded "
                      />
                    )
                  })}
                </div>
                <ul
                  role="list"
                  className="divide-y divide-gray-100 rounded-md border border-gray-200"
                >
                  <li className="flex items-center justify-between py-4 px-3 text-sm leading-6">
                    <span className="truncate font-medium">
                      resume_back_end_developer.pdf
                    </span>
                  </li>
                  <li className="flex items-center justify-between py-4 px-3 text-sm leading-6">
                    <span className="truncate font-medium">
                      resume_back_end_developer.pdf
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </li>
        </ul> */}
      </div>
    </div>
  );
}

export default Page;
