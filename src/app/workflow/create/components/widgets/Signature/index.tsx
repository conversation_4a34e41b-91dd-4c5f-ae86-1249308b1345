import { omit } from 'lodash-es';
import Image from 'next/image';
import React, { useRef, useState } from 'react';
import SignatureCanvas from 'react-signature-canvas';

import { PiArrowCounterClockwise } from '@/components/Icons';

export default function Signature(props: any) {
  const signatureRef = useRef<SignatureCanvas>(null);
  const windowSize = useRef(
    typeof window !== 'undefined'
      ? [window.innerWidth, window.innerHeight]
      : [0, 0]
  );
  const { readOnly, value, onChange, ...rest } = omit(props, [
    'addons',
    'schema'
  ]);
  const [isShowSignature, setIsShowSignature] = useState(true);

  const submit = () => {
    const dataUrl = signatureRef.current
      ?.getTrimmedCanvas()
      .toDataURL('image/png');
    onChange(dataUrl);
  };

  const handleClear = () => {
    signatureRef.current?.clear();
  };

  if (readOnly) {
    return (
      <div className="bg-white">
        {!!value && (
          <Image
            alt=""
            className="w-full object-cover max-h-[300px]"
            height="0"
            sizes="100%"
            src={value}
            width="0"
          />
        )}
      </div>
    );
  }

  return (
    <div className="relative flex flex-col items-center">
      <SignatureCanvas
        backgroundColor="rgb(247 249 255)"
        canvasProps={{
          className: 'w-full h-[300px]'
        }}
        onEnd={() => {
          submit();
        }}
        penColor="black"
        ref={signatureRef}
      />
      <div className="absolute top-3 right-3" onClick={handleClear}>
        <PiArrowCounterClockwise color="#333" fontSize={20} />
      </div>
      {isShowSignature && !!value ? (
        <>
          <Image
            alt=""
            className="absolute top-0 right-0 z-1 w-full object-cover  max-h-[300px]"
            height="0"
            onClick={() => {
              setIsShowSignature(false);
            }}
            sizes="100vw"
            src={value}
            width="0"
          />
          <div
            className="absolute top-3 right-3"
            onClick={() => {
              setIsShowSignature(false);
            }}
          >
            重签
          </div>
        </>
      ) : null}
    </div>
  );
}
