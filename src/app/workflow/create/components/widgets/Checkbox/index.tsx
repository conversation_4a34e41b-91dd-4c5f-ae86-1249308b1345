import { Checkbox as AntdCheckbox, Space } from 'antd-mobile';
import { omit } from 'lodash-es';
import React from 'react';

const isValidateArray = (list: unknown) =>
  Array.isArray(list) && list.length > 0;

const findLabels = (value: any[], options: any[]) => {
  console.log('🚀 ~ options:', options);
  console.log('🚀 ~ value:', value);
  if (!(isValidateArray(value) && isValidateArray(options))) return [];
  return value.map((v) => options.find((o) => o.value === v)?.label);
};

export default function Checkbox(props: any) {
  const { readOnly, value, options, ...rest } = omit(props, [
    'addons',
    'schema'
  ]);

  if (readOnly) {
    const __html = findLabels(value, options).join('，');
    console.log('🚀 ~ __html:', __html);
    return <div dangerouslySetInnerHTML={{ __html }} />;
  }

  return (
    <AntdCheckbox.Group {...rest}>
      <Space direction="vertical" wrap>
        {options.map((item: any) => {
          return (
            <AntdCheckbox key={item.value} value={item.value}>
              {item.label}
            </AntdCheckbox>
          );
        })}
      </Space>
    </AntdCheckbox.Group>
  );
}
