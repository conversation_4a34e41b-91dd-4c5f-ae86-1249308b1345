'use client';

import { Button, Checkbox, List, Popup, SearchBar, Toast } from 'antd-mobile';
import { omit } from 'lodash-es';
import { Plus } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useImmer } from 'use-immer';

import { getDeptUser, searchTeacher } from '@/api/common';
import { PiCaretRightBold } from '@/components/Icons';
import TeacherAvatar from './components/TeacherAvatar';

// 类型定义
interface Teacher {
  id: string;
  name: string;
  avatar: string;
  type: 'user';
}

interface TreeNode {
  id: string;
  name: string;
  level: number;
  children?: TreeNode[];
  staffList?: Array<{
    id: string;
    name: string;
    avatar: string;
  }>;
}

interface TeacherPickerWidgetProps {
  value?: Teacher[];
  onChange?: (value: Teacher[]) => void;
  readOnly?: boolean;
  placeholder?: string;
  schema?: Record<string, unknown>;
  addons?: Record<string, unknown>;
  [key: string]: unknown;
}

// 提取数据加载逻辑
function useTeacherData() {
  const [data, setData] = useState<TreeNode | null>(null);
  const [value, setValue] = useState<TreeNode | null>(null);
  const [breadcrumb, setBreadcrumb] = useImmer<TreeNode[]>([]);
  const [keyword, setKeyword] = useState('');
  const [searchList, setSearchList] = useState<Teacher[]>([]);
  const [loading, setLoading] = useState(false);

  // 移除初始化数据的自动加载

  const initData = async () => {
    try {
      setLoading(true);
      const response = await getDeptUser({ parentId: 0, fetchChild: 1 });
      const res = response?.data || response;
      setData(res);
      setValue(res);
      setBreadcrumb([{ ...res }]);
    } catch (error) {
      Toast.show({
        content: '获取部门数据失败',
        position: 'center',
      });
    } finally {
      setLoading(false);
    }
  };

  // 搜索老师
  const search = async () => {
    if (keyword !== '') {
      try {
        setLoading(true);
        const response = await searchTeacher(keyword);
        const res = response?.data || response;
        if (Array.isArray(res.list) && res.list.length) {
          const teachers = res.list.map((item: Record<string, unknown>) => ({
            id: String(item.id),
            name: String(item.name),
            avatar: String(item.avatar || ''),
            type: 'user' as const,
          }));
          setSearchList(teachers);
        } else {
          Toast.show('暂无你搜索的教师');
          setSearchList([]);
        }
      } catch (error) {
        Toast.show({
          content: '搜索失败',
          position: 'center',
        });
        setSearchList([]);
      } finally {
        setLoading(false);
      }
    }
  };

  // 获取树节点数据
  const getTreeNodeData = (id: string) => {
    if (!data) return;
    const newData = JSON.parse(JSON.stringify(data));
    const result = findInTree(newData, (node) => node.id === id);
    if (result) {
      setValue(result);
    }
  };

  // 在树中查找节点
  const findInTree = (
    tree: TreeNode,
    predicate: (data: TreeNode) => boolean
  ): TreeNode | null => {
    if (predicate(tree)) {
      setBreadcrumb((draft) => {
        const index = draft.findIndex((item) => item.id === tree.id);
        if (index === -1) {
          draft.push(tree);
        } else {
          draft.splice(index + 1);
        }
      });
      return tree;
    }

    if (Array.isArray(tree.children) && tree.children.length > 0) {
      for (const child of tree.children) {
        const result = findInTree(child, predicate);
        if (result) {
          return result;
        }
      }
    }
    return null;
  };

  return {
    data,
    value,
    breadcrumb,
    keyword,
    setKeyword,
    searchList,
    setSearchList,
    loading,
    search,
    getTreeNodeData,
    initData,
  };
}

export default function TeacherPickerWidget(props: TeacherPickerWidgetProps) {
  const {
    readOnly = false,
    value = [],
    onChange,
    placeholder = '请选择老师',
    ...rest
  } = omit(props, ['addons', 'schema']);

  const {
    value: currentNode,
    breadcrumb,
    keyword,
    setKeyword,
    searchList,
    setSearchList,
    loading,
    search,
    getTreeNodeData,
    initData,
  } = useTeacherData();

  const [visible, setVisible] = useState(false);
  const [selectedTeachers, setSelectedTeachers] = useState<Teacher[]>(
    Array.isArray(value) ? value : []
  );

  // 同步外部 value 变化
  useEffect(() => {
    if (value && Array.isArray(value)) {
      setSelectedTeachers(value);
    }
  }, [value]);

  // 只读模式渲染
  if (readOnly) {
    if (!selectedTeachers || selectedTeachers.length === 0) {
      return <div className="text-gray-400">未选择老师</div>;
    }

    return (
      <div className="flex flex-wrap gap-2">
        {selectedTeachers.map((teacher) => (
          <React.Suspense
            fallback={<div className="h-20 w-16" />}
            key={teacher.id}
          >
            <TeacherAvatar size="sm" teacher={teacher} />
          </React.Suspense>
        ))}
      </div>
    );
  }

  // 处理选择器打开
  const handlePickerOpen = async () => {
    // 如果还没有数据，先加载数据
    if (!currentNode) {
      await initData();
    }
    setVisible(true);
  };

  // 处理选择器关闭
  const handlePickerClose = () => {
    setVisible(false);
    setKeyword('');
    setSearchList([]);
  };

  // 处理确认选择
  const handleConfirm = () => {
    setVisible(false);
    if (onChange && typeof onChange === 'function') {
      onChange(selectedTeachers);
    }
    setKeyword('');
    setSearchList([]);
  };

  // 移除已选择的老师
  const handleRemoveTeacher = (teacherId: string) => {
    const newSelectedTeachers = selectedTeachers.filter(
      (t) => t.id !== teacherId
    );
    setSelectedTeachers(newSelectedTeachers);
    if (onChange && typeof onChange === 'function') {
      onChange(newSelectedTeachers);
    }
  };

  // 处理老师选择/取消选择
  const handleTeacherToggle = (teacher: Teacher, checked: boolean) => {
    if (checked) {
      const teacherExists = selectedTeachers.some((t) => t.id === teacher.id);
      if (!teacherExists) {
        setSelectedTeachers([...selectedTeachers, teacher]);
      }
    } else {
      setSelectedTeachers(selectedTeachers.filter((t) => t.id !== teacher.id));
    }
  };

  // 检查老师是否已选择
  const isTeacherSelected = (teacherId: string) => {
    return selectedTeachers.some((t) => t.id === teacherId);
  };

  return (
    <div className="teacher-picker-widget">
      <div className="space-y-3">
        <div className="flex flex-wrap items-center gap-2">
          {selectedTeachers.map((teacher) => (
            <React.Suspense
              fallback={<div className="h-20 w-16" />}
              key={teacher.id}
            >
              <TeacherAvatar
                onRemove={handleRemoveTeacher}
                size="sm"
                teacher={teacher}
              />
            </React.Suspense>
          ))}
          <button
            className="flex items-center text-primary text-sm hover:text-primary/80"
            onClick={handlePickerOpen}
            type="button"
          >
            <div className="flex h-10 w-10 items-center justify-center overflow-hidden rounded-full border-2 border-gray-400">
              <Plus className="h-4 w-4" />
            </div>
          </button>
        </div>
        {selectedTeachers.length === 0 && (
          <div className="text-gray-400 text-sm">{String(placeholder)}</div>
        )}
      </div>
      <Popup
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          minHeight: '56vh',
          maxHeight: '100vh',
          paddingTop: '10px',
        }}
        onMaskClick={handlePickerClose}
        visible={visible}
      >
        <div className="p-4 pt-2">
          <div className="mb-4 flex items-center justify-between text-base">
            <div className="text-stone-400" onClick={handlePickerClose}>
              取消
            </div>
            <div className="text-lg text-stone-900">选择老师</div>
            <div className="text-[#3B82F7]" onClick={handleConfirm}>
              确定
            </div>
          </div>

          <div className="mb-4 flex items-center">
            <div className="flex-1">
              <SearchBar
                onChange={setKeyword}
                onClear={() => {
                  setKeyword('');
                  setSearchList([]);
                }}
                style={{
                  '--border-radius': '100px',
                }}
                value={keyword}
              />
            </div>
            {keyword !== '' && (
              <div className="ml-2">
                <Button
                  color="primary"
                  loading={loading}
                  onClick={search}
                  size="small"
                >
                  搜索
                </Button>
              </div>
            )}
          </div>

          <div className="flex flex-wrap">
            {selectedTeachers.map((teacher) => (
              <div
                className="relative mb-4 flex w-1/5 flex-col items-center"
                key={teacher.id}
              >
                <TeacherAvatar
                  onRemove={(teacherId) => {
                    setSelectedTeachers((prev) =>
                      prev.filter((t) => t.id !== teacherId)
                    );
                  }}
                  size="sm"
                  teacher={teacher}
                />
              </div>
            ))}
          </div>

          {searchList.length ? (
            <div className="flex max-h-[50vh] flex-col overflow-y-scroll">
              <List
                style={{
                  '--border-top': 'none',
                  '--border-inner': 'solid 1px #F1F1F1',
                }}
              >
                {searchList.map((teacher) => (
                  <List.Item
                    arrow={false}
                    key={teacher.id}
                    prefix={
                      <div onClick={(e) => e.stopPropagation()}>
                        <Checkbox
                          checked={isTeacherSelected(teacher.id)}
                          onChange={(checked) => {
                            handleTeacherToggle(teacher, checked);
                          }}
                        />
                      </div>
                    }
                  >
                    <div className="flex flex-1 justify-between">
                      <div>{teacher.name}</div>
                    </div>
                  </List.Item>
                ))}
              </List>
            </div>
          ) : (
            <>
              <div className="mb-3 flex items-center">
                {breadcrumb.map((item) => (
                  <div
                    className="flex items-center"
                    key={item.id}
                    onClick={() => {
                      getTreeNodeData(item.id);
                    }}
                  >
                    <span className="mr-1 text-base">{item.name}</span>
                    <PiCaretRightBold color="#D8D8D8" size="18px" />
                  </div>
                ))}
              </div>
              <div className="flex max-h-[50vh] flex-col overflow-y-scroll">
                <List
                  style={{
                    '--border-top': 'none',
                    '--border-inner': 'solid 1px #F1F1F1',
                  }}
                >
                  {currentNode &&
                    Array.isArray(currentNode.children) &&
                    currentNode.children.map((item) => (
                      <List.Item arrow={false} key={item.id}>
                        <div className="flex flex-1 justify-between">
                          <div>{item.name}</div>
                          {((item.children && item.children.length > 0) ||
                            (item.staffList && item.staffList.length > 0)) && (
                            <div
                              className="flex items-center"
                              onClick={() => {
                                getTreeNodeData(item.id);
                              }}
                            >
                              <span className="mr-1 text-[#4E78FF]">下级</span>
                              <PiCaretRightBold color="#D8D8D8" size="18px" />
                            </div>
                          )}
                        </div>
                      </List.Item>
                    ))}
                  {currentNode &&
                    Array.isArray(currentNode.staffList) &&
                    currentNode.staffList.map((teacher) => (
                      <List.Item
                        arrow={false}
                        key={teacher.id}
                        prefix={
                          <div onClick={(e) => e.stopPropagation()}>
                            <Checkbox
                              checked={isTeacherSelected(teacher.id)}
                              onChange={(checked) => {
                                const teacherData: Teacher = {
                                  id: teacher.id,
                                  name: teacher.name,
                                  avatar: teacher.avatar,
                                  type: 'user',
                                };
                                handleTeacherToggle(teacherData, checked);
                              }}
                            />
                          </div>
                        }
                      >
                        <div className="flex flex-1 justify-between">
                          <div>{teacher.name}</div>
                        </div>
                      </List.Item>
                    ))}
                </List>
              </div>
            </>
          )}
        </div>
      </Popup>
    </div>
  );
}
