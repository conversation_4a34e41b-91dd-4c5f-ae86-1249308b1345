'use client';

import { Button, Dialog, Input, Toast } from 'antd-mobile';
import { omit } from 'lodash-es';
import { Edit3, Plus, Trash2 } from 'lucide-react';
import React, { useCallback, useEffect, useState } from 'react';

// 表格组件的类型定义
interface TableColumn {
  key: string;
  title: string;
  dataIndex: string;
  width?: number;
}

interface TableRow {
  [key: string]: string | number | boolean | null | undefined;
  _id: string; // 内部使用的唯一标识
}

interface TableWidgetProps {
  value?: TableRow[];
  onChange?: (value: TableRow[]) => void;
  readOnly?: boolean;
  schema?: {
    properties?: {
      columns?: TableColumn[];
    };
  };
  addons?: Record<string, unknown>;
  [key: string]: unknown;
}

// 生成唯一ID
const generateId = () =>
  `row_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// 表格行编辑弹窗组件
const RowEditModal: React.FC<{
  visible: boolean;
  columns: TableColumn[];
  rowData: TableRow | null;
  onConfirm: (data: TableRow) => void;
  onCancel: () => void;
}> = ({ visible, columns, rowData, onConfirm, onCancel }) => {
  const [formData, setFormData] = useState<
    Record<string, string | number | boolean | null | undefined>
  >({});

  React.useEffect(() => {
    if (visible && rowData) {
      setFormData({ ...rowData });
    } else if (visible && !rowData) {
      // 新增行时初始化空数据
      const initialData: Record<
        string,
        string | number | boolean | null | undefined
      > = { _id: generateId() };
      for (const col of columns) {
        initialData[col.dataIndex] = '';
      }
      setFormData(initialData);
    }
  }, [visible, rowData, columns]);

  const handleInputChange = (key: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [key]: value as string | number | boolean | null | undefined,
    }));
  };

  const handleConfirm = () => {
    // 验证必填字段
    const hasEmptyFields = columns.some(
      (col) =>
        !formData[col.dataIndex] ||
        String(formData[col.dataIndex] || '').trim() === ''
    );

    if (hasEmptyFields) {
      Toast.show({
        content: '请填写完整信息',
        position: 'center',
      });
      return;
    }

    // 确保传递的数据包含 _id 字段
    const finalRowData = {
      ...formData,
      _id: formData._id || generateId(),
    } as TableRow;

    onConfirm(finalRowData);
  };

  return (
    <Dialog
      actions={[
        {
          key: 'cancel',
          text: '取消',
          onClick: onCancel,
        },
        {
          key: 'confirm',
          text: '确定',
          bold: true,
          onClick: handleConfirm,
        },
      ]}
      content={
        <div className="space-y-4 py-4">
          {columns.map((column) => (
            <div className="space-y-2" key={column.key}>
              <label
                className="block font-medium text-gray-700 text-sm"
                htmlFor={column.key}
              >
                {column.title}
              </label>
              <Input
                onChange={(value) => handleInputChange(column.dataIndex, value)}
                placeholder={`请输入${column.title}`}
                value={String(formData[column.dataIndex] || '')}
              />
            </div>
          ))}
        </div>
      }
      title={rowData ? '编辑行' : '新增行'}
      visible={visible}
    />
  );
};

// 主表格组件
export default function TableWidget(props: TableWidgetProps) {
  const {
    onChange,
    value,
    readOnly = false,
    schema,
  } = omit(props, ['addons', 'schema']);

  // 使用内部状态管理表格数据
  const [tableData, setTableData] = useState<TableRow[]>(
    (value as TableRow[]) || []
  );

  // 当 value prop 变化时同步内部状态
  useEffect(() => {
    setTableData((value as TableRow[]) || []);
  }, [value]);

  // 从 schema 中获取列配置，如果没有则使用默认配置
  const columns: TableColumn[] = (
    schema as { properties?: { columns?: TableColumn[] } }
  )?.properties?.columns || [
    { key: 'col1', title: '列1', dataIndex: 'col1' },
    { key: 'col2', title: '列2', dataIndex: 'col2' },
    { key: 'col3', title: '列3', dataIndex: 'col3' },
  ];

  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingRow, setEditingRow] = useState<TableRow | null>(null);

  // 添加新行
  const handleAddRow = useCallback(() => {
    setEditingRow(null);
    setEditModalVisible(true);
  }, []);

  // 编辑行
  const handleEditRow = useCallback((row: TableRow) => {
    setEditingRow(row);
    setEditModalVisible(true);
  }, []);

  // 删除行
  const handleDeleteRow = useCallback(
    (rowId: string) => {
      Dialog.confirm({
        title: '确认删除',
        content: '确定要删除这一行数据吗？',
        onConfirm: () => {
          const newValue = tableData.filter((row) => row._id !== rowId);
          setTableData(newValue); // 立即更新内部状态
          if (onChange) {
            onChange(newValue);
          }
        },
      });
    },
    [tableData, onChange]
  );

  // 确认编辑/新增
  const handleRowConfirm = useCallback(
    (rowData: TableRow) => {
      let newValue: TableRow[];

      if (editingRow) {
        // 编辑现有行
        newValue = tableData.map((row) =>
          row._id === editingRow._id ? rowData : row
        );
      } else {
        // 新增行
        newValue = [...tableData, rowData];
      }

      setTableData(newValue); // 立即更新内部状态
      if (onChange) {
        console.log('🚀 ~ onChange:', onChange);
        onChange(newValue);
      }
      setEditModalVisible(false);
      setEditingRow(null);
    },
    [editingRow, tableData, onChange]
  );

  // 取消编辑
  const handleEditCancel = useCallback(() => {
    setEditModalVisible(false);
    setEditingRow(null);
  }, []);

  // 只读模式渲染
  if (readOnly) {
    if (!tableData || tableData.length === 0) {
      return <div className="py-4 text-center text-gray-400">暂无数据</div>;
    }

    return (
      <div className="w-full overflow-x-auto">
        <table className="w-full border-collapse border border-gray-300">
          <thead>
            <tr className="bg-gray-50">
              {columns.map((column) => (
                <th
                  className="border border-gray-300 px-3 py-2 text-left font-medium text-gray-700 text-sm"
                  key={column.key}
                  style={{ width: column.width }}
                >
                  {column.title}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {tableData.map((row, index) => (
              <tr className="hover:bg-gray-50" key={row._id || index}>
                {columns.map((column) => (
                  <td
                    className="border border-gray-300 px-3 py-2 text-gray-900 text-sm"
                    key={column.key}
                  >
                    {row[column.dataIndex] || '-'}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  }

  // 编辑模式渲染
  return (
    <div className="w-full space-y-4">
      {/* 表格 */}
      <div className="w-full overflow-x-auto">
        <table className="w-full border-collapse border border-gray-300">
          <thead>
            <tr className="bg-gray-50">
              {columns.map((column) => (
                <th
                  className="border border-gray-300 px-3 py-2 text-left font-medium text-gray-700 text-sm"
                  key={column.key}
                  style={{ width: column.width }}
                >
                  {column.title}
                </th>
              ))}
              <th className="w-20 border border-gray-300 px-3 py-2 text-center font-medium text-gray-700 text-sm">
                操作
              </th>
            </tr>
          </thead>
          <tbody>
            {tableData.length === 0 ? (
              <tr>
                <td
                  className="border border-gray-300 px-3 py-8 text-center text-gray-400"
                  colSpan={columns.length + 1}
                >
                  暂无数据，点击下方按钮添加
                </td>
              </tr>
            ) : (
              tableData.map((row, index) => (
                <tr className="hover:bg-gray-50" key={row._id || index}>
                  {columns.map((column) => (
                    <td
                      className="border border-gray-300 px-3 py-2 text-gray-900 text-sm"
                      key={column.key}
                    >
                      {row[column.dataIndex] || '-'}
                    </td>
                  ))}
                  <td className="border border-gray-300 px-3 py-2 text-center">
                    <div className="flex justify-center space-x-2">
                      <button
                        className="text-blue-500 hover:text-blue-700"
                        onClick={() => handleEditRow(row)}
                        type="button"
                      >
                        <Edit3 size={16} />
                      </button>
                      <button
                        className="text-red-500 hover:text-red-700"
                        onClick={() => handleDeleteRow(row._id)}
                        type="button"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* 添加按钮 */}
      <Button
        className="w-full"
        color="primary"
        fill="outline"
        onClick={handleAddRow}
      >
        <Plus className="mr-2" size={16} />
        添加行
      </Button>

      {/* 编辑弹窗 */}
      <RowEditModal
        columns={columns}
        onCancel={handleEditCancel}
        onConfirm={handleRowConfirm}
        rowData={editingRow}
        visible={editModalVisible}
      />
    </div>
  );
}
