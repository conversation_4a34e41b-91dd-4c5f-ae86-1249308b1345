import React from 'react';

import {
  Bi<PERSON>ontFamily,
  PiCheckSquare,
  PiDeviceMobileSpeaker,
  PiFolder,
  PiImage,
  PiMapPin,
  PiNote,
  PiRadioButton,
  PiSignature,
  PiTable,
  PiTextT,
  PiToggleLeft,
  PiUserFill,
  PiVideo,
  RiAttachment2,
  RiMoneyCnyCircleLine,
  RxCalendar,
  TbCalendarTime
} from '@/components/Icons';

interface IconProps {
  iconName: React.ComponentType<{ size: number; color: string }>;
  text: string;
  type: string;
  onSelect: (type: string) => void;
}

const Icon = (props: IconProps) => {
  const { iconName, text, type, onSelect } = props;
  const icon = React.createElement(iconName, { size: 24, color: '#333' });
  return (
    <button
      className="flex h-[140px] w-[150px] flex-col items-center justify-center rounded-md bg-stone-100 p-2"
      onClick={() => {
        console.log('item', type);
        onSelect(type);
      }}
      type="button"
    >
      {icon}
      <span className="mt-1 text-xs">{text}</span>
    </button>
  );
};

const ItemList = [
  {
    name: '单行文本',
    iconName: PiTextT,
    type: 'input'
  },
  {
    name: '多行文本',
    iconName: BiFontFamily,
    type: 'textArea'
  },
  {
    name: '说明文字',
    iconName: PiNote,
    type: 'richText'
  },
  {
    name: '数字',
    iconName: BiFontFamily,
    type: 'inputNumber'
  },
  {
    name: '金额',
    iconName: RiMoneyCnyCircleLine,
    type: 'amountNumber'
  },
  {
    name: '日期',
    iconName: RxCalendar,
    type: 'date'
  },
  {
    name: '日期-时间',
    iconName: TbCalendarTime,
    type: 'dateTime'
  },
  {
    name: '单选',
    iconName: PiRadioButton,
    type: 'picker'
  },
  {
    name: '多选',
    iconName: PiCheckSquare,
    type: 'checkbox'
  },
  {
    name: '开关',
    iconName: PiToggleLeft,
    type: 'switch'
  },
  {
    name: '地址',
    iconName: PiMapPin,
    type: 'address'
  },
  {
    name: '图片',
    iconName: PiImage,
    type: 'image'
  },
  {
    name: '视频',
    iconName: PiVideo,
    type: 'video'
  },
  {
    name: '附件',
    iconName: RiAttachment2,
    type: 'attachment'
  },
  {
    name: '签名',
    iconName: PiSignature,
    type: 'signature'
  },
  {
    name: '手机号',
    iconName: PiDeviceMobileSpeaker,
    type: 'phoneNumber'
  },
  {
    name: '选择学生',
    iconName: PiUserFill,
    type: 'studentPicker'
  },
  {
    name: '选择老师',
    iconName: PiUserFill,
    type: 'teacherPicker'
  },
  {
    name: '表格',
    iconName: PiTable,
    type: 'table'
  },
  {
    name: '分组',
    iconName: PiFolder,
    type: 'group'
  }
];

interface IndexProps {
  onSelect: (type: string) => void;
  filterTypes?: (type: string) => boolean;
}

function Index({ onSelect, filterTypes }: IndexProps) {
  const filteredItems = filterTypes
    ? ItemList.filter((item) => filterTypes(item.type))
    : ItemList;

  return (
    <div className="grid grid-cols-4 gap-4 p-4">
      {filteredItems.map((item) => (
        <Icon
          iconName={item.iconName}
          key={item.type}
          onSelect={onSelect}
          text={item.name}
          type={item.type}
        />
      ))}
    </div>
  );
}

export default Index;
