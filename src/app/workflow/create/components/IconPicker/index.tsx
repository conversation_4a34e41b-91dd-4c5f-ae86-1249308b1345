import { ImageUploader, Popup, Tabs } from 'antd-mobile';
import Image from 'next/image';
import React, { forwardRef, useImperativeHandle, useState } from 'react';

import { upload } from '../utils/upload';

export interface Ref {
  toggle(): void;
}
interface Props {
  icons: string[];
  onSelect: (icon: string) => void;
}

const IconPicker = forwardRef<Ref, Props>((props, ref) => {
  const { onSelect, icons } = props;
  const [iconPopupVisible, setIconPopupVisible] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);

  useImperativeHandle(ref, () => ({
    toggle() {
      setIconPopupVisible((prev) => !prev);
    },
  }));

  return (
    <Popup
      bodyStyle={{
        borderTopLeftRadius: '8px',
        borderTopRightRadius: '8px',
        minHeight: '56vh',
        paddingTop: '10px',
      }}
      onMaskClick={() => {
        setIconPopupVisible(false);
      }}
      visible={iconPopupVisible}
    >
      <Tabs
        activeLineMode="fixed"
        style={{
          '--fixed-active-line-width': '30px',
        }}
      >
        <Tabs.Tab className="w-1/2" key="1" title="默认图标">
          <div className="grid grid-cols-5 gap-4 p-2">
            {icons.map((item, index) => (
              <div
                className="flex justify-center"
                key={`icon${index}`}
                onClick={() => {
                  setIconPopupVisible(false);
                  onSelect(item);
                }}
              >
                <Image alt="" height="60" src={item} width="60" />
              </div>
            ))}
          </div>
        </Tabs.Tab>
        <Tabs.Tab className="w-1/2" key="2" title="自定义图标">
          <div className="p-2">
            <ImageUploader
              maxCount={1}
              onChange={setFileList}
              onDelete={() => {
                onSelect('');
              }}
              onPreview={(_, file) => {
                onSelect(file.url);
                setIconPopupVisible(false);
              }}
              preview={false}
              upload={upload as any}
              value={fileList}
            />
          </div>
        </Tabs.Tab>
      </Tabs>
    </Popup>
  );
});

export default IconPicker;
