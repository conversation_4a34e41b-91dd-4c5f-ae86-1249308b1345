import {
  Button,
  Form,
  Input,
  NavBar,
  Switch,
  TextArea,
  Toast,
} from 'antd-mobile';
import type { FormInstance } from 'antd-mobile/es/components/form';
import { useEffect, useRef } from 'react';

import { PiTrash } from '@/components/Icons';

import type { ItemType } from '../../types';

interface Props {
  widgetId: string;
  updateFormItem: (id: string, data: any) => void;
  forms: ItemType[];
  setWidgetId: (id: string) => void;
  updateGroupChildItem?: (
    groupId: string,
    childId: string,
    item: ItemType
  ) => void;
}

export default function Index({
  widgetId,
  updateFormItem,
  forms,
  setWidgetId,
  updateGroupChildItem,
}: Props) {
  const formRef = useRef<FormInstance>(null);

  console.log('widgetId', widgetId);

  // 查找控件数据，包括分组内的子控件
  const findWidgetData = (widgetId: string): ItemType | undefined => {
    // 首先在表单根级别查找
    const rootWidget = forms.find((item) => item.id === widgetId);
    if (rootWidget) {
      return rootWidget;
    }

    // 在分组内查找
    for (const form of forms) {
      if (form.widget === 'group' && form.properties) {
        const childWidget = Object.values(form.properties).find(
          (child: any) => child.id === widgetId
        );
        if (childWidget) {
          return childWidget as ItemType;
        }
      }
    }

    return;
  };

  const widgetData: ItemType | undefined = findWidgetData(widgetId);

  useEffect(() => {
    if (widgetData) {
      console.log('🚀 ~ file: page.tsx:28 ~ widgetData:', widgetData);
      formRef.current?.setFieldsValue(widgetData);
    }
  }, [widgetData]);

  // 查找控件所属的分组ID
  const findParentGroupId = (childId: string): string | null => {
    for (const form of forms) {
      if (form.widget === 'group' && form.properties) {
        const childWidget = Object.values(form.properties).find(
          (child: any) => child.id === childId
        );
        if (childWidget) {
          return form.id;
        }
      }
    }
    return null;
  };

  const onFinish = (values: any) => {
    console.log('🚀 ~ file: page.tsx:34 ~ values:', values);
    if (!widgetId) {
      return;
    }
    const data = { ...values, id: widgetId };

    // 判断是否为分组内的子控件
    const parentGroupId = findParentGroupId(widgetId);
    if (parentGroupId && updateGroupChildItem) {
      // 更新分组内的子控件
      updateGroupChildItem(parentGroupId, widgetId, data);
    } else {
      // 更新根级别控件
      updateFormItem(widgetId, data);
    }

    setWidgetId('');
    formRef.current?.resetFields();
  };

  const removeOption = (index: number) => {
    const value = formRef.current?.getFieldValue(['props', 'options']);
    if (value.length === 1) {
      Toast.show({
        content: '至少保留一个选项',
      });
      return;
    }
    const newValue = value.filter((item: any, i: number) => i !== index);
    formRef.current?.setFieldsValue({
      props: {
        options: newValue,
      },
    });
  };

  return (
    <div className="max-h-screen overflow-scroll bg-gray-50">
      <NavBar
        onBack={() => {
          setWidgetId('');
        }}
      >
        设置控件
      </NavBar>
      <Form
        footer={
          <Button block color="primary" type="submit">
            确定
          </Button>
        }
        mode="card"
        onFinish={onFinish}
        ref={formRef}
      >
        <Form.Header>设置控件{widgetData?.title || ''}</Form.Header>
        <Form.Item label="控件名称" name="title" rules={[{ required: true }]}>
          <Input maxLength={20} placeholder="请输入控件名称" />
        </Form.Item>
        <Form.Item label="控件说明" name="description">
          <Input maxLength={50} placeholder="请输入控件说明" />
        </Form.Item>
        <Form.Item hidden label="数据类型" name="type">
          <Input />
        </Form.Item>
        <Form.Item hidden label="默认提示" name="placeholder">
          <Input />
        </Form.Item>
        <Form.Item hidden label="组件名" name="widget">
          <Input />
        </Form.Item>
        <Form.Item hidden label="组件名" name="readOnlyWidget">
          <Input />
        </Form.Item>
        <Form.Item hidden label="组件名" name="layout">
          <Input />
        </Form.Item>
        <Form.Item hidden label="属性" name="properties">
          <Input />
        </Form.Item>
        {widgetData?.widget ? (
          <>
            {['picker', 'checkbox', 'checkboxes'].includes(
              widgetData?.widget
            ) && (
              <Form.Item label="添加选项" name="props">
                <Form.Array
                  name={['props', 'options']}
                  onAdd={(operation) => {
                    const value = formRef.current?.getFieldValue([
                      'props',
                      'options',
                    ]);
                    console.log('🚀 ~ file: index.tsx:51 ~ value:', value);
                    operation.add({
                      label: `选项${value.length + 1}`,
                      value: String(value.length + 1),
                    });
                  }}
                  renderAdd={() => (
                    <Button color="primary" fill="outline">
                      添加选项
                    </Button>
                  )}
                >
                  {(fields) => {
                    return fields.map(({ index }) => (
                      <Form.Item
                        extra={
                          <button
                            onClick={() => {
                              removeOption(index);
                            }}
                            type="button"
                          >
                            <PiTrash color="#666" fontSize={18} />
                          </button>
                        }
                        key={`item${index}`}
                        name={[index, 'label']}
                        rules={[
                          { required: true, message: '选项名称不能为空' },
                        ]}
                      >
                        <Input maxLength={20} placeholder="请输入选项名称" />
                      </Form.Item>
                    ));
                  }}
                </Form.Array>
              </Form.Item>
            )}
            {widgetData?.widget === 'richText' && (
              <Form.Item label="说明文字" name="props" noStyle>
                <Form.Item
                  className="h-[400px]"
                  label="说明文字"
                  // rules={[{ required: true, message: '说明文字不能为空' }]}
                  name={['props', 'value']}
                >
                  <TextArea maxLength={500} showCount />
                </Form.Item>
              </Form.Item>
            )}
            {widgetData?.widget === 'group' && (
              <div className="mb-4 rounded-lg bg-blue-50 p-3">
                <div className="mb-2 text-blue-600 text-sm">📁 分组设置</div>
                <div className="text-gray-600 text-xs">
                  分组用于将相关的控件组织在一起，提高表单的可读性和逻辑性。
                  您可以在分组内添加各种控件，但不能嵌套其他分组。
                </div>
              </div>
            )}
          </>
        ) : null}
        {widgetData?.widget !== 'richText' && (
          <Form.Item
            childElementPosition="right"
            label="是否必填"
            layout="horizontal"
            name="required"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        )}
        <Form.Item
          childElementPosition="right"
          help="若取消勾选，打印时将不显示本控件"
          label="参与打印"
          layout="horizontal"
          name="printable"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>
      </Form>
    </div>
  );
}
